﻿namespace LeadTeams.Models.ViewModel.AttendanceLog
{
    public class CreateAttendanceLogViewModel : BaseOrganizationCreateViewModel, IEntityMapper<AttendanceLogModel, CreateAttendanceLogViewModel>
    {
        [CustomRequired]
        [DisplayName("IP Address")]
        [MaxLength(20)]
        public string AttendanceLogIPAddress { get; set; }
        [CustomRequired]
        [DisplayName("IP Address Type")]
        [MaxLength(10)]
        public string AttendanceLogIPAddressType { get; set; }
        [CustomRequired]
        [DisplayName("TimeZone")]
        public string AttendanceLogTimeZone { get; set; }
        [CustomRequired]
        [DisplayName("Log DateTime")]
        public DateTime AttendanceLogDateTime { get; set; }
        [CustomRequired]
        [DisplayName("Log Status")]
        [MaxLength(15)]
        public string AttendanceLogStatus { get; set; }
        [CustomRequired]
        [DisplayName("Employee Shift")]
        public string AttendanceLogEmployeeShift { get; set; }

        [CustomRequired]
        [Browsable(false)]
        public Ulid EmployeeId { get; set; }

        [Browsable(false)]
        [DisplayName("Task")]
        public Ulid? TaskId { get; set; }

        public CreateAttendanceLogViewModel ToDto(AttendanceLogModel entity) => entity.ToCreateDto();

        public AttendanceLogModel ToEntity() => AttendanceLogMapper.ToEntity(this);
    }
}
