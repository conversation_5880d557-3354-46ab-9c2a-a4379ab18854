using LeadTeams.API.Attributes;

namespace LeadTeams.API.Models.Timezone
{
    /// <summary>
    /// Base response model that includes timezone information for API clients.
    /// Use this when you need to provide timezone context with your responses.
    /// </summary>
    public class TimezoneAwareResponse<T>
    {
        /// <summary>
        /// The actual response data.
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// Server timezone information.
        /// </summary>
        public TimezoneInfo ServerTimezone { get; set; }

        /// <summary>
        /// Client timezone information (if provided in request).
        /// </summary>
        public TimezoneInfo? ClientTimezone { get; set; }

        /// <summary>
        /// UTC timestamp when the response was generated.
        /// </summary>
        public DateTime ResponseTimestampUtc { get; set; }

        public TimezoneAwareResponse(T data)
        {
            Data = data;
            ServerTimezone = new TimezoneInfo
            {
                Id = TimeZoneInfo.Local.Id,
                DisplayName = TimeZoneInfo.Local.DisplayName,
                StandardName = TimeZoneInfo.Local.StandardName,
                OffsetFromUtc = TimeZoneInfo.Local.GetUtcOffset(DateTime.Now).ToString(@"hh\:mm")
            };
            ResponseTimestampUtc = DateTime.UtcNow;
        }

        public void SetClientTimezone(string? timezoneId, TimeSpan? offset = null)
        {
            if (!string.IsNullOrEmpty(timezoneId))
            {
                try
                {
                    var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                    ClientTimezone = new TimezoneInfo
                    {
                        Id = tz.Id,
                        DisplayName = tz.DisplayName,
                        StandardName = tz.StandardName,
                        OffsetFromUtc = tz.GetUtcOffset(DateTime.Now).ToString(@"hh\:mm")
                    };
                }
                catch (TimeZoneNotFoundException)
                {
                    if (offset.HasValue)
                    {
                        ClientTimezone = new TimezoneInfo
                        {
                            Id = $"UTC{(offset.Value >= TimeSpan.Zero ? "+" : "")}{offset.Value:hh\\:mm}",
                            DisplayName = $"UTC{(offset.Value >= TimeSpan.Zero ? "+" : "")}{offset.Value:hh\\:mm}",
                            StandardName = $"UTC{(offset.Value >= TimeSpan.Zero ? "+" : "")}{offset.Value:hh\\:mm}",
                            OffsetFromUtc = offset.Value.ToString(@"hh\:mm")
                        };
                    }
                }
            }
        }
    }

    /// <summary>
    /// Timezone information model for API responses.
    /// </summary>
    public class TimezoneInfo
    {
        /// <summary>
        /// Timezone identifier (e.g., "America/New_York", "Europe/London").
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Human-readable timezone display name.
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Standard timezone name.
        /// </summary>
        public string StandardName { get; set; } = string.Empty;

        /// <summary>
        /// Current offset from UTC (e.g., "-05:00", "+02:00").
        /// </summary>
        public string OffsetFromUtc { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for DateTime-heavy data that includes both UTC and client-local times.
    /// Useful for attendance logs, meeting schedules, etc.
    /// </summary>
    public class DateTimeAwareResponse<T>
    {
        /// <summary>
        /// The actual response data with UTC DateTime values.
        /// </summary>
        public T DataUtc { get; set; }

        /// <summary>
        /// The same data with DateTime values converted to client's timezone (if timezone provided).
        /// </summary>
        public T? DataClientTimezone { get; set; }

        /// <summary>
        /// Timezone information.
        /// </summary>
        public TimezoneInfo ServerTimezone { get; set; }

        /// <summary>
        /// Client timezone information (if provided).
        /// </summary>
        public TimezoneInfo? ClientTimezone { get; set; }

        /// <summary>
        /// Instructions for the client on how to handle DateTime values.
        /// </summary>
        public string DateTimeHandlingInstructions { get; set; }

        public DateTimeAwareResponse(T data)
        {
            DataUtc = data;
            ServerTimezone = new TimezoneInfo
            {
                Id = TimeZoneInfo.Local.Id,
                DisplayName = TimeZoneInfo.Local.DisplayName,
                StandardName = TimeZoneInfo.Local.StandardName,
                OffsetFromUtc = TimeZoneInfo.Local.GetUtcOffset(DateTime.Now).ToString(@"hh\:mm")
            };
            DateTimeHandlingInstructions = "All DateTime values in 'DataUtc' are in UTC format (ISO 8601 with 'Z' suffix). " +
                                         "If 'DataClientTimezone' is provided, it contains the same data converted to your timezone. " +
                                         "For consistent behavior, always use UTC values and convert to local time on the client side.";
        }
    }

    /// <summary>
    /// Extension methods for creating timezone-aware responses.
    /// </summary>
    public static class TimezoneResponseExtensions
    {
        /// <summary>
        /// Creates a timezone-aware response with client timezone information.
        /// </summary>
        public static TimezoneAwareResponse<T> ToTimezoneAwareResponse<T>(this T data, HttpContext context)
        {
            var response = new TimezoneAwareResponse<T>(data);
            response.SetClientTimezone(context.GetClientTimezone(), context.GetClientTimezoneOffset());
            return response;
        }

        /// <summary>
        /// Creates a DateTime-aware response with both UTC and client timezone data.
        /// </summary>
        public static DateTimeAwareResponse<T> ToDateTimeAwareResponse<T>(this T data, HttpContext context)
        {
            var response = new DateTimeAwareResponse<T>(data);

            var clientTimezone = context.GetClientTimezone();
            var clientOffset = context.GetClientTimezoneOffset();

            if (!string.IsNullOrEmpty(clientTimezone) || clientOffset.HasValue)
            {
                response.ClientTimezone = new TimezoneInfo();
                response.SetClientTimezone(clientTimezone, clientOffset);

                // Note: Converting DataClientTimezone would require reflection or custom logic
                // depending on your data structure. This is a placeholder for the concept.
                // You might want to implement specific converters for your DTOs.
            }

            return response;
        }

        private static void SetClientTimezone<T>(this DateTimeAwareResponse<T> response, string? timezoneId, TimeSpan? offset)
        {
            if (!string.IsNullOrEmpty(timezoneId))
            {
                try
                {
                    var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                    response.ClientTimezone = new TimezoneInfo
                    {
                        Id = tz.Id,
                        DisplayName = tz.DisplayName,
                        StandardName = tz.StandardName,
                        OffsetFromUtc = tz.GetUtcOffset(DateTime.Now).ToString(@"hh\:mm")
                    };
                }
                catch (TimeZoneNotFoundException)
                {
                    if (offset.HasValue)
                    {
                        response.ClientTimezone = new TimezoneInfo
                        {
                            Id = $"UTC{(offset.Value >= TimeSpan.Zero ? "+" : "")}{offset.Value:hh\\:mm}",
                            DisplayName = $"UTC{(offset.Value >= TimeSpan.Zero ? "+" : "")}{offset.Value:hh\\:mm}",
                            StandardName = $"UTC{(offset.Value >= TimeSpan.Zero ? "+" : "")}{offset.Value:hh\\:mm}",
                            OffsetFromUtc = offset.Value.ToString(@"hh\:mm")
                        };
                    }
                }
            }
        }
    }
}
