using System.Globalization;

namespace LeadTeams.PermissionAndSession.Authentication
{
    /// <summary>
    /// Timezone provider that gets timezone from user session/claims.
    /// Use this when timezone is stored in user profile or JWT claims.
    /// </summary>
    public class SessionTimeZoneProvider : ITimeZoneProvider
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public SessionTimeZoneProvider(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                    // Fallback to system timezone
                    return new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();

                // Try to get timezone from user claims
                var timezoneClaim = httpContext.User?.FindFirst("timezone")?.Value;
                if (!string.IsNullOrEmpty(timezoneClaim))
                {
                    return Task.FromResult<TimezoneInfo?>(new TimezoneInfo
                    {
                        Id = timezoneClaim,
                        Offset = GetOffsetFromTimezoneId(timezoneClaim),
                        Culture = httpContext.User?.FindFirst("culture")?.Value ?? CultureInfo.CurrentCulture.Name
                    });
                }

                // Try to get timezone from session
                var sessionTimezone = httpContext.Session?.GetString("UserTimezone");
                if (!string.IsNullOrEmpty(sessionTimezone))
                {
                    return Task.FromResult<TimezoneInfo?>(new TimezoneInfo
                    {
                        Id = sessionTimezone,
                        Offset = GetOffsetFromTimezoneId(sessionTimezone),
                        Culture = httpContext.Session?.GetString("UserCulture") ?? CultureInfo.CurrentCulture.Name
                    });
                }

                // Fallback to system timezone
                return new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
            catch
            {
                return Task.FromResult<TimezoneInfo?>(null);
            }
        }

        private string? GetOffsetFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                var offset = tz.GetUtcOffset(DateTime.Now);
                var sign = offset >= TimeSpan.Zero ? "+" : "-";
                var absOffset = offset.Duration();
                return $"{sign}{absOffset:hh\\:mm}";
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Timezone provider that gets timezone from database user profile.
    /// Use this when timezone preference is stored in user database record.
    /// </summary>
    public class DatabaseTimeZoneProvider : ITimeZoneProvider
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserServiceProvider _userService; // You'll need to inject your user service

        public DatabaseTimeZoneProvider(IHttpContextAccessor httpContextAccessor, IUserServiceProvider userService)
        {
            _httpContextAccessor = httpContextAccessor;
            _userService = userService;
        }

        public async Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                    return null;

                // Get user ID from claims or session
                var userIdClaim = httpContext.User?.FindFirst("sub")?.Value ??
                                 httpContext.User?.FindFirst("userId")?.Value;

                if (string.IsNullOrEmpty(userIdClaim))
                    return null;

                // Get user timezone preference from database
                var userProfile = await _userService.GetUserProfileAsync(userIdClaim);
                if (userProfile?.TimeZone != null)
                {
                    return new TimezoneInfo
                    {
                        Id = userProfile.TimeZone,
                        Offset = GetOffsetFromTimezoneId(userProfile.TimeZone),
                        Culture = userProfile.Culture ?? CultureInfo.CurrentCulture.Name,
                        DisplayName = GetDisplayNameFromTimezoneId(userProfile.TimeZone)
                    };
                }

                // Fallback to system timezone
                return await new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
            catch
            {
                return null;
            }
        }

        private string? GetOffsetFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                var offset = tz.GetUtcOffset(DateTime.Now);
                var sign = offset >= TimeSpan.Zero ? "+" : "-";
                var absOffset = offset.Duration();
                return $"{sign}{absOffset:hh\\:mm}";
            }
            catch
            {
                return null;
            }
        }

        private string? GetDisplayNameFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                return tz.DisplayName;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Timezone provider that gets timezone from HTTP request headers.
    /// Use this when client applications send timezone information in headers.
    /// </summary>
    public class HeaderTimeZoneProvider : ITimeZoneProvider
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public HeaderTimeZoneProvider(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                    return new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();

                var request = httpContext.Request;

                // Check for timezone ID in headers
                if (request.Headers.TryGetValue("X-Timezone", out var timezoneValues))
                {
                    var timezoneId = timezoneValues.FirstOrDefault();
                    if (!string.IsNullOrEmpty(timezoneId))
                    {
                        return Task.FromResult<TimezoneInfo?>(new TimezoneInfo
                        {
                            Id = timezoneId,
                            Offset = GetOffsetFromTimezoneId(timezoneId),
                            Culture = request.Headers.TryGetValue("X-Culture", out var cultureValues)
                                ? cultureValues.FirstOrDefault() ?? CultureInfo.CurrentCulture.Name
                                : CultureInfo.CurrentCulture.Name
                        });
                    }
                }

                // Check for timezone offset in headers
                if (request.Headers.TryGetValue("X-Timezone-Offset", out var offsetValues))
                {
                    var offset = offsetValues.FirstOrDefault();
                    if (!string.IsNullOrEmpty(offset))
                    {
                        return Task.FromResult<TimezoneInfo?>(new TimezoneInfo
                        {
                            Id = $"UTC{offset}",
                            Offset = offset,
                            Culture = request.Headers.TryGetValue("X-Culture", out var cultureValues)
                                ? cultureValues.FirstOrDefault() ?? CultureInfo.CurrentCulture.Name
                                : CultureInfo.CurrentCulture.Name
                        });
                    }
                }

                // Fallback to system timezone
                return new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
            catch
            {
                return Task.FromResult<TimezoneInfo?>(null);
            }
        }

        private string? GetOffsetFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                var offset = tz.GetUtcOffset(DateTime.Now);
                var sign = offset >= TimeSpan.Zero ? "+" : "-";
                var absOffset = offset.Duration();
                return $"{sign}{absOffset:hh\\:mm}";
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Composite timezone provider that tries multiple sources in order.
    /// Use this to implement a fallback chain for timezone detection.
    /// </summary>
    public class CompositeTimeZoneProvider : ITimeZoneProvider
    {
        private readonly IEnumerable<ITimeZoneProvider> _providers;

        public CompositeTimeZoneProvider(IEnumerable<ITimeZoneProvider> providers)
        {
            _providers = providers;
        }

        public async Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            foreach (var provider in _providers)
            {
                try
                {
                    var result = await provider.GetCurrentTimezoneAsync();
                    if (result != null && !string.IsNullOrEmpty(result.Id))
                    {
                        return result;
                    }
                }
                catch
                {
                    // Continue to next provider
                }
            }

            return null;
        }
    }

    /// <summary>
    /// Timezone provider for desktop applications that gets timezone from application user context.
    /// Use this for WPF, WinForms, or other desktop applications.
    /// </summary>
    public class DesktopTimeZoneProvider : ITimeZoneProvider
    {
        private readonly IDesktopUserContext _userContext;

        public DesktopTimeZoneProvider(IDesktopUserContext userContext)
        {
            _userContext = userContext;
        }

        public async Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            try
            {
                // Get timezone from desktop user context
                var currentUser = await _userContext.GetCurrentUserAsync();
                if (currentUser?.TimeZone != null)
                {
                    return new TimezoneInfo
                    {
                        Id = currentUser.TimeZone,
                        Offset = GetOffsetFromTimezoneId(currentUser.TimeZone),
                        Culture = currentUser.Culture ?? CultureInfo.CurrentCulture.Name,
                        DisplayName = GetDisplayNameFromTimezoneId(currentUser.TimeZone)
                    };
                }

                // Fallback to system timezone
                return await new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
            catch
            {
                return await new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
        }

        private string? GetOffsetFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                var offset = tz.GetUtcOffset(DateTime.Now);
                var sign = offset >= TimeSpan.Zero ? "+" : "-";
                var absOffset = offset.Duration();
                return $"{sign}{absOffset:hh\\:mm}";
            }
            catch
            {
                return null;
            }
        }

        private string? GetDisplayNameFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                return tz.DisplayName;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Timezone provider for desktop applications that stores timezone in application settings.
    /// </summary>
    public class DesktopSettingsTimeZoneProvider : ITimeZoneProvider
    {
        private readonly IDesktopSettingsService _settingsService;

        public DesktopSettingsTimeZoneProvider(IDesktopSettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        public Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            try
            {
                var timezoneId = _settingsService.GetSetting("UserTimezone");
                var culture = _settingsService.GetSetting("UserCulture") ?? CultureInfo.CurrentCulture.Name;

                if (!string.IsNullOrEmpty(timezoneId))
                {
                    return Task.FromResult<TimezoneInfo?>(new TimezoneInfo
                    {
                        Id = timezoneId,
                        Offset = GetOffsetFromTimezoneId(timezoneId),
                        Culture = culture,
                        DisplayName = GetDisplayNameFromTimezoneId(timezoneId)
                    });
                }

                // Fallback to system timezone
                return new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
            catch
            {
                return new DefaultTimeZoneProvider().GetCurrentTimezoneAsync();
            }
        }

        private string? GetOffsetFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                var offset = tz.GetUtcOffset(DateTime.Now);
                var sign = offset >= TimeSpan.Zero ? "+" : "-";
                var absOffset = offset.Duration();
                return $"{sign}{absOffset:hh\\:mm}";
            }
            catch
            {
                return null;
            }
        }

        private string? GetDisplayNameFromTimezoneId(string timezoneId)
        {
            try
            {
                var tz = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
                return tz.DisplayName;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Interface for desktop user context (replaces HttpContext for desktop apps).
    /// </summary>
    public interface IDesktopUserContext
    {
        Task<DesktopUser?> GetCurrentUserAsync();
        void SetCurrentUser(DesktopUser user);
        void ClearCurrentUser();
    }

    /// <summary>
    /// Interface for desktop settings service.
    /// </summary>
    public interface IDesktopSettingsService
    {
        string? GetSetting(string key);
        void SetSetting(string key, string value);
        void SaveSettings();
    }

    /// <summary>
    /// Desktop user model.
    /// </summary>
    public class DesktopUser
    {
        public string? UserId { get; set; }
        public string? UserName { get; set; }
        public string? TimeZone { get; set; }
        public string? Culture { get; set; }
        public DateTime? LastLogin { get; set; }
    }

    /// <summary>
    /// Interface for user service (you'll need to implement this based on your user management system).
    /// </summary>
    public interface IUserServiceProvider
    {
        Task<UserProfile?> GetUserProfileAsync(string userId);
    }

    /// <summary>
    /// User profile model (adjust based on your actual user model).
    /// </summary>
    public class UserProfile
    {
        public string? TimeZone { get; set; }
        public string? Culture { get; set; }
        // Add other user profile properties as needed
    }
}
