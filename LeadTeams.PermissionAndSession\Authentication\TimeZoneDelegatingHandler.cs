﻿namespace LeadTeams.PermissionAndSession.Authentication
{
    public class TimeZoneDelegatingHandler : DelegatingHandler
    {
        public TimeZoneDelegatingHandler()
        {
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            // Send the request
            var response = await base.SendAsync(request, cancellationToken);
            return response;
        }
    }
}
