﻿using System.Globalization;

namespace LeadTeams.PermissionAndSession.Authentication
{
    /// <summary>
    /// HTTP message handler that automatically adds timezone information to outgoing requests.
    /// This ensures all API calls include the current user's timezone context.
    /// </summary>
    public class TimeZoneDelegatingHandler : DelegatingHandler
    {
        private readonly ITimeZoneProvider _timeZoneProvider;

        public TimeZoneDelegatingHandler(ITimeZoneProvider? timeZoneProvider = null)
        {
            _timeZoneProvider = timeZoneProvider ?? new DefaultTimeZoneProvider();
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            // Add timezone information to the request headers if not already present
            await AddTimezoneHeadersAsync(request);

            // Send the request
            var response = await base.SendAsync(request, cancellationToken);
            return response;
        }

        private async Task AddTimezoneHeadersAsync(HttpRequestMessage request)
        {
            try
            {
                // Check if timezone headers are already present
                if (request.Headers.Contains("X-Timezone") || request.Headers.Contains("X-Timezone-Offset"))
                {
                    return; // Don't override existing timezone information
                }

                // Get timezone information from the provider
                var timezoneInfo = await _timeZoneProvider.GetCurrentTimezoneAsync();

                if (timezoneInfo != null)
                {
                    // Add timezone ID header (preferred method)
                    if (!string.IsNullOrEmpty(timezoneInfo.Id))
                    {
                        request.Headers.Add("X-Timezone", timezoneInfo.Id);
                    }

                    // Add timezone offset header as fallback
                    if (!string.IsNullOrEmpty(timezoneInfo.Offset))
                    {
                        request.Headers.Add("X-Timezone-Offset", timezoneInfo.Offset);
                    }

                    // Add culture information if available
                    if (!string.IsNullOrEmpty(timezoneInfo.Culture))
                    {
                        request.Headers.Add("X-Culture", timezoneInfo.Culture);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the request
                // You might want to inject ILogger here for proper logging
                System.Diagnostics.Debug.WriteLine($"Failed to add timezone headers: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Interface for providing timezone information.
    /// </summary>
    public interface ITimeZoneProvider
    {
        Task<TimezoneInfo?> GetCurrentTimezoneAsync();
    }

    /// <summary>
    /// Timezone information model.
    /// </summary>
    public class TimezoneInfo
    {
        public string? Id { get; set; }
        public string? Offset { get; set; }
        public string? Culture { get; set; }
        public string? DisplayName { get; set; }
    }

    /// <summary>
    /// Default timezone provider that uses system timezone.
    /// </summary>
    public class DefaultTimeZoneProvider : ITimeZoneProvider
    {
        public Task<TimezoneInfo?> GetCurrentTimezoneAsync()
        {
            try
            {
                var systemTimeZone = TimeZoneInfo.Local;
                var currentOffset = systemTimeZone.GetUtcOffset(DateTime.Now);

                var timezoneInfo = new TimezoneInfo
                {
                    Id = systemTimeZone.Id,
                    Offset = FormatOffset(currentOffset),
                    Culture = CultureInfo.CurrentCulture.Name,
                    DisplayName = systemTimeZone.DisplayName
                };

                return Task.FromResult<TimezoneInfo?>(timezoneInfo);
            }
            catch
            {
                return Task.FromResult<TimezoneInfo?>(null);
            }
        }

        private static string FormatOffset(TimeSpan offset)
        {
            var sign = offset >= TimeSpan.Zero ? "+" : "-";
            var absOffset = offset.Duration();
            return $"{sign}{absOffset:hh\\:mm}";
        }
    }
}
