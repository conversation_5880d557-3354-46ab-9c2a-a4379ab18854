namespace LeadTeams.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .CreateLogger();

            builder.Services.AddSerilog(Log.Logger);

            builder.Services.AddControllers().AddJsonOptions(option =>
            {
                option.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.Preserve;

                // Add UTC DateTime converters for global API consistency
                option.JsonSerializerOptions.Converters.Add(new UtcDateTimeJsonConverter());
                option.JsonSerializerOptions.Converters.Add(new UtcNullableDateTimeJsonConverter());
                option.JsonSerializerOptions.Converters.Add(new UtcDateTimeOffsetJsonConverter());
            });
            builder.Services.AddEndpointsApiExplorer();

            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.InjectAuthenticationServices();
            builder.Services.AddSwaggerGen(opt =>
            {
                opt.SwaggerDoc("v1", new OpenApiInfo { Title = "LeadTeams API", Version = "v1" });
                opt.AddSecurityDefinition(AuthenticationDefaults.AuthenticationScheme, new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Description = "Please enter token",
                    Name = AuthenticationDefaults.AuthenticationHeader,
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Scheme = AuthenticationDefaults.AuthenticationScheme,
                });
                opt.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type=ReferenceType.SecurityScheme,
                                Id=AuthenticationDefaults.AuthenticationScheme,
                            }
                        },
                        new string[]{}
                    }
                });
            });

            builder.Services.AddHttpContextAccessor();
            builder.Services.AddTransient<PermissionAndSession.Session.ISession, CloudSession>();
            builder.Services.AddTransient<IAuthenticationValidationService, AuthenticationHttpValidationService>();
            builder.Services.InjectServiceServices();

            // SignalR
            builder.Services.UseSignalRChat();

            var app = builder.Build();

            // if (app.Environment.IsDevelopment())
            // {
            app.UseSwagger();
            app.UseSwaggerUI();
            // }

            app.UseRouting();
            app.UseMiddleware<TokenMiddleware>();
            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllers();

            // SignalR
            // app.UseSignalRChatIntegration();
            app.MapHub<LeadTeamsHub>(SignalRChatIntegrationHelper.EndPoints.LeadTeams);

            app.Run();
        }
    }
}
