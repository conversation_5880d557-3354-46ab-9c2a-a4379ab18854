﻿namespace LeadTeams.Models.Model
{
    [Table("AttendanceLog")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<AttendanceLogModel>))]
    [NotAuditable]
    public partial class AttendanceLogModel : BaseOrganizationWithoutTrackingModel
    {
        private string _AttendanceLogIPAddress = null!;
        private string _AttendanceLogIPAddressType = null!;
        private string _AttendanceLogTimeZone = null!;
        private DateTime _AttendanceLogDate = DateTime.Now;
        private string _AttendanceLogStatus = null!;
        private Ulid _EmployeeId;
        private Ulid? _TaskId;

        [CustomRequired]
        [DisplayName("IP Address")]
        [MaxLength(20)]
        [Column(TypeName = "nvarchar(max)")]
        public string AttendanceLogIPAddress { get => _AttendanceLogIPAddress; set => this.CheckPropertyChanged(ref _AttendanceLogIPAddress, ref value); }
        [CustomRequired]
        [DisplayName("IP Address Type")]
        [MaxLength(10)]
        [Column(TypeName = "nvarchar(max)")]
        public string AttendanceLogIPAddressType { get => _AttendanceLogIPAddressType; set => this.CheckPropertyChanged(ref _AttendanceLogIPAddressType, ref value); }
        [CustomRequired]
        [DisplayName("TimeZone")]
        [Column(TypeName = "LongText")]
        public string AttendanceLogTimeZone { get => _AttendanceLogTimeZone; set => this.CheckPropertyChanged(ref _AttendanceLogTimeZone, ref value); }
        [CustomRequired]
        [DisplayName("Log DateTime")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime AttendanceLogDateTime { get => _AttendanceLogDate; set => this.CheckPropertyChanged(ref _AttendanceLogDate, ref value); }
        [CustomRequired]
        [DisplayName("Log Status")]
        [MaxLength(15)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(Enums.MonitoringStatus))]
        public string AttendanceLogStatus { get => _AttendanceLogStatus; set => this.CheckPropertyChanged(ref _AttendanceLogStatus, ref value); }
        [CustomRequired]
        [DisplayName("Employee Shift")]
        [Column(TypeName = "LongText")]
        public string AttendanceLogEmployeeShift { get; set; }

        [CustomRequired]
        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(EmployeeModel))]
        public Ulid EmployeeId { get => _EmployeeId; set => this.CheckPropertyChanged(ref _EmployeeId, ref value); }
        public virtual EmployeeModel Employee { get; set; }

        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(TaskModel))]
        [DisplayName("Task")]
        public Ulid? TaskId { get => _TaskId; set => this.CheckPropertyChanged(ref _TaskId, ref value); }
        [Browsable(false)]
        public virtual TaskModel? Task { get; set; }
    }
}
