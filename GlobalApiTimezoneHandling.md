# 🌍 Global API DateTime & Timezone Handling Solution

## 📋 **Complete Solution Overview**

This document provides a comprehensive solution for handling DateTime values in your LeadTeams API when serving clients across different timezones worldwide.

## 🎯 **Key Principles & Best Practices**

### **✅ Recommended API Strategy:**
1. **Store everything in UTC** (database layer handles this)
2. **API communicates in UTC** (JSON serialization enforces this)
3. **Clients handle local conversion** (client responsibility)
4. **Provide timezone context** when needed (optional enhancement)

### **🔄 Data Flow:**
```
Client (Local Time) → API (UTC) → Database (UTC) → API (UTC) → Client (Local Time)
```

## 🛠️ **Implementation Components**

### **1. UTC JSON Converters** (`UtcDateTimeJsonConverter.cs`)
**Purpose:** Ensures all DateTime values in API requests/responses are in UTC format.

**Features:**
- ✅ Converts incoming DateTime to UTC regardless of client timezone
- ✅ Always serializes DateTime as UTC with 'Z' suffix (ISO 8601)
- ✅ Handles DateTime, DateTime?, and DateTimeOffset
- ✅ Automatic timezone detection and conversion

**Usage:** Automatically applied to all API endpoints via Program.cs configuration.

### **2. Timezone Detection** (`TimezoneAttribute.cs`)
**Purpose:** Captures client timezone information from API requests.

**Detection Methods (Priority Order):**
1. **`X-Timezone` header** (e.g., "America/New_York", "Europe/London")
2. **`X-Timezone-Offset` header** (e.g., "-05:00", "+02:00")
3. **Query parameter** (`?timezone=America/New_York`)

**Usage:**
```csharp
[Timezone] // Apply to controller or individual actions
public class MyController : ControllerBase
{
    public IActionResult MyAction()
    {
        var clientTz = HttpContext.GetClientTimezone();
        // Use timezone information as needed
    }
}
```

### **3. Timezone-Aware Responses** (`TimezoneAwareResponse.cs`)
**Purpose:** Provides timezone context in API responses when needed.

**Features:**
- ✅ Includes server timezone information
- ✅ Includes client timezone information (if provided)
- ✅ Response timestamp in UTC
- ✅ Clear instructions for DateTime handling

## 📝 **Client Integration Guide**

### **For API Clients:**

#### **1. Sending DateTime Values**
```javascript
// ✅ RECOMMENDED: Send UTC DateTime
const meetingTime = new Date().toISOString(); // "2024-01-15T19:47:00.000Z"

// ✅ ALTERNATIVE: Send local time with timezone header
fetch('/api/meetings', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-Timezone': 'America/New_York' // Client's timezone
    },
    body: JSON.stringify({
        title: 'Team Meeting',
        meetingTime: '2024-01-15T14:47:00' // Local time, API will convert to UTC
    })
});
```

#### **2. Receiving DateTime Values**
```javascript
// All DateTime values from API are in UTC format
const response = await fetch('/api/attendance/GetEmployeeAttendanceLog');
const data = await response.json();

// Convert to local time on client side
data.Data.forEach(record => {
    const localCheckIn = new Date(record.CheckIn); // Automatic local conversion
    console.log('Check-in (local):', localCheckIn.toLocaleString());
});
```

#### **3. Timezone Headers**
```javascript
// Option 1: Send timezone ID
headers: { 'X-Timezone': 'America/New_York' }

// Option 2: Send timezone offset
headers: { 'X-Timezone-Offset': '-05:00' }

// Option 3: Query parameter
fetch('/api/data?timezone=Europe/London')
```

## 🔧 **Implementation Examples**

### **Example 1: Basic Controller with Timezone Support**
```csharp
[Timezone]
[ApiController]
public class MeetingsController : ControllerBase
{
    [HttpPost]
    public IActionResult CreateMeeting([FromBody] CreateMeetingRequest request)
    {
        // Convert client time to UTC for database storage
        var meetingTimeUtc = HttpContext.ToUtcFromClientTimezone(request.MeetingTime);
        
        // Save to database (EF converters handle UTC storage)
        var meeting = new Meeting { MeetingTime = meetingTimeUtc };
        
        // Return timezone-aware response
        return Ok(meeting.ToTimezoneAwareResponse(HttpContext));
    }
}
```

### **Example 2: Attendance Report with Timezone Handling**
```csharp
[HttpGet("attendance")]
public IActionResult GetAttendance(DateTime? startDate, DateTime? endDate)
{
    // Convert client dates to UTC for database queries
    var startUtc = startDate.HasValue ? HttpContext.ToUtcFromClientTimezone(startDate.Value) : null;
    var endUtc = endDate.HasValue ? HttpContext.ToUtcFromClientTimezone(endDate.Value) : null;
    
    var data = _service.GetAttendance(startUtc, endUtc);
    
    return Ok(new {
        Data = data, // UTC DateTime values
        QueryPeriod = new {
            StartUtc = startUtc,
            EndUtc = endUtc,
            ClientTimezone = HttpContext.GetClientTimezone()
        }
    });
}
```

## 🧪 **Testing Your Implementation**

### **Test Scenarios:**

#### **1. Client in New York (UTC-5)**
```bash
curl -X GET "https://api.leadteams.com/api/attendance" \
  -H "X-Timezone: America/New_York" \
  -H "Content-Type: application/json"
```

#### **2. Client in Tokyo (UTC+9)**
```bash
curl -X POST "https://api.leadteams.com/api/meetings" \
  -H "X-Timezone-Offset: +09:00" \
  -H "Content-Type: application/json" \
  -d '{"title":"Meeting","meetingTime":"2024-01-15T14:00:00"}'
```

#### **3. Client without timezone (defaults to UTC)**
```bash
curl -X GET "https://api.leadteams.com/api/server-time"
```

## 🚨 **Important Considerations**

### **Database Layer:**
- ✅ **EF Converters handle UTC storage** (already implemented)
- ✅ **All DateTime properties stored as UTC**
- ✅ **Consistent data regardless of server timezone**

### **API Layer:**
- ✅ **JSON converters ensure UTC communication**
- ✅ **Timezone detection for client context**
- ✅ **Optional timezone-aware responses**

### **Client Layer:**
- ⚠️ **Clients must handle local timezone conversion**
- ⚠️ **Send timezone information for accurate conversion**
- ⚠️ **Always expect UTC DateTime values from API**

## 📊 **Migration Strategy**

### **For Existing APIs:**
1. **Phase 1:** Add JSON converters (non-breaking)
2. **Phase 2:** Add timezone detection to critical endpoints
3. **Phase 3:** Update client applications to send timezone headers
4. **Phase 4:** Enhance responses with timezone context

### **For New APIs:**
- ✅ Use `[Timezone]` attribute on all DateTime-related endpoints
- ✅ Return timezone-aware responses for complex DateTime data
- ✅ Document timezone requirements for clients

## 🎉 **Benefits of This Solution**

1. **✅ Global Consistency:** All DateTime values standardized to UTC
2. **✅ Timezone Awareness:** Optional client timezone detection and conversion
3. **✅ Backward Compatible:** Existing clients continue to work
4. **✅ Future Proof:** Scalable for global expansion
5. **✅ Developer Friendly:** Clear patterns and documentation
6. **✅ Database Integrity:** UTC storage ensures data consistency

## 🔗 **Quick Reference**

### **Headers to Send:**
- `X-Timezone: "America/New_York"` (timezone ID)
- `X-Timezone-Offset: "-05:00"` (offset from UTC)

### **DateTime Format:**
- **API Input/Output:** `"2024-01-15T19:47:00.000Z"` (UTC with Z suffix)
- **Client Display:** Convert to local timezone for user interface

### **Key Classes:**
- `UtcDateTimeJsonConverter` - JSON serialization
- `TimezoneAttribute` - Timezone detection
- `TimezoneAwareResponse<T>` - Enhanced responses
- `TimezoneExtensions` - Helper methods
