using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace LeadTeams.Repositories.EF.Factory.Converters
{
    /// <summary>
    /// Converts ULID values to byte arrays for efficient database storage.
    /// ULIDs are stored as 16-byte arrays in the database for optimal performance and storage.
    /// </summary>
    public class UlidToBytesConverter : ValueConverter<Ulid, byte[]>
    {
        private static readonly ConverterMappingHints DefaultHints = new ConverterMappingHints(size: 16);

        public UlidToBytesConverter() : this(null)
        {
        }

        public UlidToBytesConverter(ConverterMappingHints? mappingHints)
            : base(
                    convertToProviderExpression: x => x.ToByteArray(),
                    convertFromProviderExpression: x => new Ulid(x),
                    mappingHints: DefaultHints.With(mappingHints))
        {
        }
    }
}
