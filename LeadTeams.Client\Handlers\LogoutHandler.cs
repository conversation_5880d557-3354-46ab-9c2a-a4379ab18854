﻿namespace LeadTeams.Client.Handlers
{
    internal class LogoutHandler
    {
        private readonly ISession _session;
        private readonly IEmployeeTrackerView _view;
        private readonly IScreenShotSaver _screenShotSaver;
        private readonly StopHandler _stopHandler;

        internal LogoutHandler(ISession session, IEmployeeTrackerView view, IScreenShotSaver screenShotSaver, StopHandler stopHandler)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Start presenter");
            _session.AppLogger.LogInformation($"Setting variables");
            _view = view;
            _screenShotSaver = screenShotSaver;
            _stopHandler = stopHandler;
        }

        internal async Task Logout()
        {
            try
            {
                _session.AppLogger.LogDebug($"Start logging out");
                // Set status to logged out
                _view.MonitoringStatus = Enums.MonitoringStatus.LoggedOut;
                _session.AppLogger.LogDebug($"Stop wotking");
                await _stopHandler.StopWorking(_view.MonitoringStatus);

                // Save the last screenshot
                _session.AppLogger.LogDebug($"Save screenshot before logging out");
                await _screenShotSaver.SaveScreenShotAsync(_view.TaskModel, _view.MonitoringStatus);

                // Allow closing the view
                _session.AppLogger.LogDebug($"Set Closable to true");
                _view.Closable = true;

                // Dispose session and tracker properly
                _session.AppLogger.LogDebug($"Disposing resources");
                DisposeResources();

                // Close the current view
                _session.AppLogger.LogDebug($"Close EmployeeTracker view");
                _view.CloseView();

                // Try opening the login view
                _session.AppLogger.LogDebug($"Opening Login view");
                OpenLoginView();
            }
            catch (Exception ex)
            {
                _view.Message = "An error occurred while logging out. Please restart the application.";
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }

        private void DisposeResources()
        {
            try
            {
                _session.AppLogger.LogDebug($"Disposing SubscribeSession");
            }
            catch (Exception ex)
            {
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }

        private void OpenLoginView()
        {
            try
            {
                _session.AppLogger.LogDebug($"Open the LoginView instance");
                LoginView loginView = GetInjectedServices.GetFormByType<LoginView>();
                loginView.Show();
            }
            catch (Exception ex)
            {
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }
    }
}
