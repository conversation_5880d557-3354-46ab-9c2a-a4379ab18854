﻿namespace LeadTeams.Models.Model
{
    [Table("ScreenShotsMonitoring")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ScreenShotsMonitoringModel>))]
    [NotAuditable]
    public class ScreenShotsMonitoringModel : BaseOrganizationWithoutTrackingModel
    {
        private DateTime _ScreenShotsMonitoringDateTime = DateTime.Now;
        private string _ScreenShotsMonitoringImageName = null!;
        private string _ScreenShotsMonitoringImagePath = null!;
        private byte[] _ScreenShotsMonitoringImage = null!;
        private string _ScreenShotsMonitoringStatus = null!;
        private Ulid _EmployeeId;
        private Ulid? _TaskId;

        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image DateTime")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime ScreenShotsMonitoringDateTime { get => _ScreenShotsMonitoringDateTime; set => this.CheckPropertyChanged(ref _ScreenShotsMonitoringDateTime, ref value); }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image Name")]
        [MaxLength(150)]
        [Column(TypeName = "nvarchar(max)")]
        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string ScreenShotsMonitoringImageName { get => _ScreenShotsMonitoringImageName; set => this.CheckPropertyChanged(ref _ScreenShotsMonitoringImageName, ref value); }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image Path")]
        [MaxLength(150)]
        [Column(TypeName = "nvarchar(max)")]
        [Browsable(false)]
        public string ScreenShotsMonitoringImagePath { get => _ScreenShotsMonitoringImagePath; set => this.CheckPropertyChanged(ref _ScreenShotsMonitoringImagePath, ref value); }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image")]
        public byte[] ScreenShotsMonitoringImage { get => _ScreenShotsMonitoringImage; set => this.CheckPropertyChanged(ref _ScreenShotsMonitoringImage, ref value); }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Status")]
        [MaxLength(15)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(Enums.MonitoringStatus))]
        public string ScreenShotsMonitoringStatus { get => _ScreenShotsMonitoringStatus; set => this.CheckPropertyChanged(ref _ScreenShotsMonitoringStatus, ref value); }

        [CustomRequired]
        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(EmployeeModel))]
        [DisplayName("Employee")]
        public Ulid EmployeeId { get => _EmployeeId; set => this.CheckPropertyChanged(ref _EmployeeId, ref value); }
        public virtual EmployeeModel Employee { get; set; } = null!;

        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(TaskModel))]
        [DisplayName("Task")]
        public Ulid? TaskId { get => _TaskId; set => this.CheckPropertyChanged(ref _TaskId, ref value); }
        [Browsable(false)]
        public virtual TaskModel? Task { get; set; } = null!;
    }
}
