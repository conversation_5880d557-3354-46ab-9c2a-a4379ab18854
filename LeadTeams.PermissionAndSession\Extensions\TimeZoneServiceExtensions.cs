namespace LeadTeams.PermissionAndSession.Extensions
{
    /// <summary>
    /// Extension methods for configuring timezone services.
    /// </summary>
    public static class TimeZoneServiceExtensions
    {
        /// <summary>
        /// Adds timezone handling services with default system timezone provider.
        /// </summary>
        public static IServiceCollection AddTimezoneHandling(this IServiceCollection services)
        {
            services.AddSingleton<ITimeZoneProvider, DefaultTimeZoneProvider>();
            services.AddTransient<TimeZoneDelegatingHandler>();
            return services;
        }

        /// <summary>
        /// Adds timezone handling services with session-based timezone provider.
        /// </summary>
        public static IServiceCollection AddSessionTimezoneHandling(this IServiceCollection services)
        {
            services.AddHttpContextAccessor();
            services.AddSingleton<ITimeZoneProvider, SessionTimeZoneProvider>();
            services.AddTransient<TimeZoneDelegatingHandler>();
            return services;
        }

        /// <summary>
        /// Adds timezone handling services with database-based timezone provider.
        /// </summary>
        public static IServiceCollection AddDatabaseTimezoneHandling<TUserService>(this IServiceCollection services)
            where TUserService : class, IUserServiceProvider
        {
            services.AddHttpContextAccessor();
            services.AddScoped<IUserServiceProvider, TUserService>();
            services.AddSingleton<ITimeZoneProvider, DatabaseTimeZoneProvider>();
            services.AddTransient<TimeZoneDelegatingHandler>();
            return services;
        }

        /// <summary>
        /// Adds timezone handling services with header-based timezone provider.
        /// </summary>
        public static IServiceCollection AddHeaderTimezoneHandling(this IServiceCollection services)
        {
            services.AddHttpContextAccessor();
            services.AddSingleton<ITimeZoneProvider, HeaderTimeZoneProvider>();
            services.AddTransient<TimeZoneDelegatingHandler>();
            return services;
        }

        /// <summary>
        /// Adds timezone handling services with composite timezone provider (multiple fallback sources).
        /// </summary>
        public static IServiceCollection AddCompositeTimezoneHandling<TUserService>(this IServiceCollection services)
            where TUserService : class, IUserServiceProvider
        {
            services.AddHttpContextAccessor();
            services.AddScoped<IUserServiceProvider, TUserService>();

            // Register individual providers
            services.AddTransient<HeaderTimeZoneProvider>();
            services.AddTransient<SessionTimeZoneProvider>();
            services.AddTransient<DatabaseTimeZoneProvider>();
            services.AddTransient<DefaultTimeZoneProvider>();

            // Register composite provider with fallback order
            services.AddSingleton<ITimeZoneProvider>(serviceProvider =>
            {
                var providers = new List<ITimeZoneProvider>
                {
                    serviceProvider.GetRequiredService<HeaderTimeZoneProvider>(),     // 1st: Check headers
                    serviceProvider.GetRequiredService<SessionTimeZoneProvider>(),   // 2nd: Check session/claims
                    serviceProvider.GetRequiredService<DatabaseTimeZoneProvider>(),  // 3rd: Check database
                    serviceProvider.GetRequiredService<DefaultTimeZoneProvider>()    // 4th: Use system timezone
                };
                return new CompositeTimeZoneProvider(providers);
            });

            services.AddTransient<TimeZoneDelegatingHandler>();
            return services;
        }

        /// <summary>
        /// Adds timezone handling services with custom timezone provider.
        /// </summary>
        public static IServiceCollection AddCustomTimezoneHandling<TTimeZoneProvider>(this IServiceCollection services)
            where TTimeZoneProvider : class, ITimeZoneProvider
        {
            services.AddSingleton<ITimeZoneProvider, TTimeZoneProvider>();
            services.AddTransient<TimeZoneDelegatingHandler>();
            return services;
        }

        /// <summary>
        /// Configures HttpClient to use timezone delegating handler.
        /// </summary>
        public static IHttpClientBuilder AddTimezoneHandler(this IHttpClientBuilder builder)
        {
            return builder.AddHttpMessageHandler<TimeZoneDelegatingHandler>();
        }

        /// <summary>
        /// Configures named HttpClient to use timezone delegating handler.
        /// </summary>
        public static IServiceCollection AddHttpClientWithTimezone(this IServiceCollection services, string name, Action<HttpClient>? configureClient = null)
        {
            var builder = services.AddHttpClient(name, configureClient ?? (_ => { }));
            builder.AddTimezoneHandler();
            return services;
        }

        /// <summary>
        /// Configures typed HttpClient to use timezone delegating handler.
        /// </summary>
        public static IServiceCollection AddHttpClientWithTimezone<TClient>(this IServiceCollection services, Action<HttpClient>? configureClient = null)
            where TClient : class
        {
            var builder = services.AddHttpClient<TClient>(configureClient ?? (_ => { }));
            builder.AddTimezoneHandler();
            return services;
        }
    }
}

/// <summary>
/// Configuration options for timezone handling.
/// </summary>
public class TimezoneHandlingOptions
{
    /// <summary>
    /// Whether to override existing timezone headers in requests.
    /// Default: false (preserves existing headers).
    /// </summary>
    public bool OverrideExistingHeaders { get; set; } = false;

    /// <summary>
    /// Whether to add culture information to requests.
    /// Default: true.
    /// </summary>
    public bool IncludeCultureInfo { get; set; } = true;

    /// <summary>
    /// Whether to log timezone detection failures.
    /// Default: true.
    /// </summary>
    public bool LogFailures { get; set; } = true;

    /// <summary>
    /// Fallback timezone ID to use when detection fails.
    /// Default: null (uses system timezone).
    /// </summary>
    public string? FallbackTimezoneId { get; set; }
}
