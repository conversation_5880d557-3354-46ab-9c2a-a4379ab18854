using Microsoft.AspNetCore.Mvc.Filters;

namespace LeadTeams.API.Attributes
{
    /// <summary>
    /// Attribute to capture client timezone information from API requests.
    /// Can be applied to controllers or individual actions.
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class TimezoneAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            // Try to get timezone from various sources
            var timezone = GetClientTimezone(context);
            
            if (!string.IsNullOrEmpty(timezone))
            {
                // Store timezone in HttpContext for use in the action
                context.HttpContext.Items["ClientTimezone"] = timezone;
                
                // Try to parse as TimeZoneInfo for validation
                try
                {
                    var timezoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timezone);
                    context.HttpContext.Items["ClientTimezoneInfo"] = timezoneInfo;
                }
                catch (TimeZoneNotFoundException)
                {
                    // If timezone ID is invalid, try to parse as offset
                    if (TryParseTimezoneOffset(timezone, out var offset))
                    {
                        context.HttpContext.Items["ClientTimezoneOffset"] = offset;
                    }
                }
            }

            base.OnActionExecuting(context);
        }

        private string? GetClientTimezone(ActionExecutingContext context)
        {
            var request = context.HttpContext.Request;

            // Priority order for timezone detection:
            // 1. X-Timezone header (recommended)
            // 2. X-Timezone-Offset header (fallback)
            // 3. Query parameter
            // 4. User-Agent parsing (last resort)

            // 1. Check X-Timezone header (e.g., "America/New_York", "Europe/London")
            if (request.Headers.TryGetValue("X-Timezone", out var timezoneHeader))
            {
                return timezoneHeader.FirstOrDefault();
            }

            // 2. Check X-Timezone-Offset header (e.g., "-05:00", "+02:00")
            if (request.Headers.TryGetValue("X-Timezone-Offset", out var offsetHeader))
            {
                return offsetHeader.FirstOrDefault();
            }

            // 3. Check query parameter
            if (request.Query.TryGetValue("timezone", out var timezoneQuery))
            {
                return timezoneQuery.FirstOrDefault();
            }

            // 4. Could add User-Agent parsing here if needed
            // This would be less reliable but could work as a last resort

            return null;
        }

        private bool TryParseTimezoneOffset(string offsetString, out TimeSpan offset)
        {
            offset = TimeSpan.Zero;

            if (string.IsNullOrEmpty(offsetString))
                return false;

            // Handle formats like "+05:00", "-08:00", "+0530", "-0800"
            offsetString = offsetString.Trim();

            if (offsetString.Length < 3)
                return false;

            var sign = offsetString[0] == '+' ? 1 : offsetString[0] == '-' ? -1 : 0;
            if (sign == 0)
                return false;

            var timeString = offsetString.Substring(1);

            // Try different formats
            if (TimeSpan.TryParse(timeString, out var parsedOffset))
            {
                offset = TimeSpan.FromTicks(sign * parsedOffset.Ticks);
                return true;
            }

            // Try format without colon (e.g., "0530" -> "05:30")
            if (timeString.Length == 4 && int.TryParse(timeString, out var numericOffset))
            {
                var hours = numericOffset / 100;
                var minutes = numericOffset % 100;
                
                if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59)
                {
                    offset = new TimeSpan(sign * hours, sign * minutes, 0);
                    return true;
                }
            }

            return false;
        }
    }

    /// <summary>
    /// Extension methods for accessing timezone information from HttpContext.
    /// </summary>
    public static class TimezoneExtensions
    {
        /// <summary>
        /// Gets the client timezone string from HttpContext.
        /// </summary>
        public static string? GetClientTimezone(this HttpContext context)
        {
            return context.Items["ClientTimezone"] as string;
        }

        /// <summary>
        /// Gets the client TimeZoneInfo from HttpContext.
        /// </summary>
        public static TimeZoneInfo? GetClientTimezoneInfo(this HttpContext context)
        {
            return context.Items["ClientTimezoneInfo"] as TimeZoneInfo;
        }

        /// <summary>
        /// Gets the client timezone offset from HttpContext.
        /// </summary>
        public static TimeSpan? GetClientTimezoneOffset(this HttpContext context)
        {
            return context.Items["ClientTimezoneOffset"] as TimeSpan?;
        }

        /// <summary>
        /// Converts a UTC DateTime to the client's timezone.
        /// </summary>
        public static DateTime ToClientTimezone(this HttpContext context, DateTime utcDateTime)
        {
            if (utcDateTime.Kind != DateTimeKind.Utc)
                utcDateTime = DateTime.SpecifyKind(utcDateTime, DateTimeKind.Utc);

            var timezoneInfo = context.GetClientTimezoneInfo();
            if (timezoneInfo != null)
            {
                return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timezoneInfo);
            }

            var offset = context.GetClientTimezoneOffset();
            if (offset.HasValue)
            {
                return utcDateTime.Add(offset.Value);
            }

            // Fallback to UTC if no timezone information available
            return utcDateTime;
        }

        /// <summary>
        /// Converts a client timezone DateTime to UTC.
        /// </summary>
        public static DateTime ToUtcFromClientTimezone(this HttpContext context, DateTime clientDateTime)
        {
            var timezoneInfo = context.GetClientTimezoneInfo();
            if (timezoneInfo != null)
            {
                return TimeZoneInfo.ConvertTimeToUtc(clientDateTime, timezoneInfo);
            }

            var offset = context.GetClientTimezoneOffset();
            if (offset.HasValue)
            {
                return clientDateTime.Subtract(offset.Value);
            }

            // Assume it's already UTC if no timezone information available
            return DateTime.SpecifyKind(clientDateTime, DateTimeKind.Utc);
        }
    }
}
