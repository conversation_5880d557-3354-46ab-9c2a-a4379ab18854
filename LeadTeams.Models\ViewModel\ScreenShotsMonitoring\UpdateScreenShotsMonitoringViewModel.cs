﻿namespace LeadTeams.Models.ViewModel.ScreenShotsMonitoring
{
    public class UpdateScreenShotsMonitoringViewModel : BaseOrganizationUpdateViewModel, IEntityMapper<ScreenShotsMonitoringModel, UpdateScreenShotsMonitoringViewModel>
    {
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image DateTime")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        public DateTime ScreenShotsMonitoringDateTime { get; set; }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image Name")]
        [MaxLength(150)]
        [Column(TypeName = "nvarchar(max)")]
        public string ScreenShotsMonitoringImageName { get; set; }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image Path")]
        [MaxLength(150)]
        [Column(TypeName = "nvarchar(max)")]
        public string ScreenShotsMonitoringImagePath { get; set; }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image")]
        public byte[] ScreenShotsMonitoringImage { get; set; }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Status")]
        [MaxLength(15)]
        [Column(TypeName = "nvarchar(max)")]
        public string ScreenShotsMonitoringStatus { get; set; }

        [CustomRequired]
        [DisplayName("Employee")]
        public Ulid EmployeeId { get; set; }

        [DisplayName("Task")]
        public Ulid? TaskId { get; set; }

        public UpdateScreenShotsMonitoringViewModel ToDto(ScreenShotsMonitoringModel entity) => entity.ToUpdateDto();

        public ScreenShotsMonitoringModel ToEntity() => ScreenShotsMonitoringMapper.ToEntity(this);
    }
}
