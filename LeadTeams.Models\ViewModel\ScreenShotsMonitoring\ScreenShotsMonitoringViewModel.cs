﻿namespace LeadTeams.Models.ViewModel.ScreenShotsMonitoring
{
    public class ScreenShotsMonitoringViewModel : BaseIdentityModel
    {
        [Browsable(false)]
        public Ulid OrganizationId { get; set; }

        #region ScreenShotsMonitoring
        [DisplayName("Screen Shots Monitoring Image Date")]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime? ScreenShotsMonitoringDateTime { get; set; }
        [CustomRequired]
        [DisplayName("Screen Shots Monitoring Image")]
        public byte[]? ScreenShotsMonitoringImage { get; set; }
        [DisplayName("Screen Shots Monitoring Status")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(Enums.MonitoringStatus))]
        public string? ScreenShotsMonitoringStatus { get; set; }
        #endregion

        #region Task
        [DisplayName("Task Id")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(TaskModel))]
        public Ulid? TaskId { get; set; }
        [DisplayName("Task Name")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string? TaskName { get; set; }
        #endregion

        #region Employee
        [DisplayName("Employee ID")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(EmployeeModel))]
        public Ulid EmployeeId { get; set; }
        [DisplayName("Employee CustomI ID")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string EmployeeCustomID { get; set; }
        [DisplayName("Employee Name")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string EmployeeName { get; set; }
        [DisplayName("Job Title")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string? EmployeeJobTitle { get; set; }
        #endregion

        #region Project
        [DisplayName("Project Id")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(ProjectModel))]
        public Ulid? ProjectId { get; set; }
        [DisplayName("Project Name")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string? ProjectName { get; set; }
        #endregion
    }
}
