using LeadTeamsConverters = LeadTeams.Repositories.EF.Factory.Converters;

namespace LeadTeams.Repositories.EF.Factory.Contexts
{
    public enum DBContextType
    {
        MySQL,
        SQLite,
    }

    public class ApplicationDBContext : DbContext
    {
        protected ApplicationDBContext(DbContextOptions options) : base(options)
        {
        }

        public virtual DbSet<AskLeaveModel> AskLeaves { get; set; }
        public virtual DbSet<AttendanceLogModel> AttendanceLogs { get; set; }
        public virtual DbSet<AuditModel> Audits { get; set; }
        public virtual DbSet<EmployeeAllowanceModel> EmployeeAllowances { get; set; }
        public virtual DbSet<EmployeeModel> Employees { get; set; }
        public virtual DbSet<ManagementTeamModel> ManagementTeams { get; set; }
        public virtual DbSet<MeetingEmployeeModel> MeetingEmployees { get; set; }
        public virtual DbSet<MeetingModel> Meetings { get; set; }
        public virtual DbSet<MessageModel> Messages { get; set; }
        public virtual DbSet<OrganizationModel> Organizations { get; set; }
        public virtual DbSet<OrganizationNewsModel> OrganizationNews { get; set; }
        public virtual DbSet<ProjectModel> Projects { get; set; }
        public virtual DbSet<ScreensAccessProfileModel> ScreensAccessProfiles { get; set; }
        public virtual DbSet<ScreensAccessProfileDetailsModel> ScreensAccessProfileDetails { get; set; }
        public virtual DbSet<ScreenShotsMonitoringModel> ScreenShotsMonitorings { get; set; }
        public virtual DbSet<ShiftModel> Shifts { get; set; }
        public virtual DbSet<ShiftDynamicPatternModel> ShiftDynamicPatterns { get; set; }
        public virtual DbSet<ShiftFixedPatternModel> ShiftFixedPatterns { get; set; }
        public virtual DbSet<TaskModel> Tasks { get; set; }
        public virtual DbSet<UserModel> Users { get; set; }

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder.Properties<decimal>().HavePrecision(18, 5);
            configurationBuilder.Properties<Ulid>().HaveConversion<LeadTeamsConverters.UlidToBytesConverter>();
            configurationBuilder.Properties<DateTime>().HaveConversion<LeadTeamsConverters.DateTimeConverter>();
            configurationBuilder.Properties<DateTimeOffset>().HaveConversion<LeadTeamsConverters.DateTimeOffsetConverter>();

            base.ConfigureConventions(configurationBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDBContext).Assembly);
            modelBuilder.Ignore<BaseScreensAccessProfileModel>();
            modelBuilder.Ignore<BaseScreensAccessProfileDetailsModel>();
            modelBuilder.Ignore<BaseOrganizationWithTrackingModel>();
            modelBuilder.Ignore<BaseOrganizationWithoutTrackingModel>();

            base.OnModelCreating(modelBuilder);
        }
    }
}
