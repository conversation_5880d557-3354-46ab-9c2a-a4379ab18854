﻿namespace LeadTeams.Client.Popups
{
    partial class StopPopupView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new LeadTeamsTableLayoutPanel();
            lblHeader = new LeadTeamsLabel();
            btnRemindme = new LeadTeamsButton();
            btnNo = new LeadTeamsButton();
            tlpMain.SuspendLayout();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.ColumnCount = 2;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            tlpMain.Controls.Add(lblHeader, 0, 0);
            tlpMain.Controls.Add(btnRemindme, 0, 1);
            tlpMain.Controls.Add(btnNo, 1, 1);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.ForeColor = Color.FromArgb(22, 71, 117);
            tlpMain.Location = new Point(0, 0);
            tlpMain.Margin = new Padding(4);
            tlpMain.Name = "tlpMain";
            tlpMain.RightToLeft = RightToLeft.No;
            tlpMain.RowCount = 2;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 70F));
            tlpMain.Size = new Size(334, 161);
            tlpMain.TabIndex = 1;
            // 
            // lblHeader
            // 
            lblHeader.AutoSize = true;
            lblHeader.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.SetColumnSpan(lblHeader, 2);
            lblHeader.Dock = DockStyle.Fill;
            lblHeader.FlatStyle = FlatStyle.Flat;
            lblHeader.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblHeader.ForeColor = Color.FromArgb(22, 71, 117);
            lblHeader.Location = new Point(4, 0);
            lblHeader.Margin = new Padding(4, 0, 4, 0);
            lblHeader.MinimumSize = new Size(64, 36);
            lblHeader.Name = "lblHeader";
            lblHeader.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            lblHeader.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            lblHeader.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            lblHeader.RadiusProperties.BorderColor = Color.Transparent;
            lblHeader.RadiusProperties.BorderRadius = 10;
            lblHeader.RadiusProperties.BorderSize = 0;
            lblHeader.RightToLeft = RightToLeft.No;
            lblHeader.Size = new Size(326, 91);
            lblHeader.TabIndex = 1;
            lblHeader.Text = "...............";
            lblHeader.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // btnRemindme
            // 
            btnRemindme.BackColor = Color.FromArgb(22, 71, 117);
            btnRemindme.Dock = DockStyle.Fill;
            btnRemindme.FlatStyle = FlatStyle.Flat;
            btnRemindme.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnRemindme.ForeColor = Color.FromArgb(234, 242, 248);
            btnRemindme.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnRemindme.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnRemindme.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnRemindme.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnRemindme.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnRemindme.Location = new Point(4, 95);
            btnRemindme.Margin = new Padding(4);
            btnRemindme.MinimumSize = new Size(64, 36);
            btnRemindme.Name = "btnRemindme";
            btnRemindme.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnRemindme.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnRemindme.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnRemindme.RadiusProperties.BorderColor = Color.Transparent;
            btnRemindme.RadiusProperties.BorderRadius = 10;
            btnRemindme.RadiusProperties.BorderSize = 0;
            btnRemindme.RightToLeft = RightToLeft.No;
            btnRemindme.Size = new Size(159, 62);
            btnRemindme.TabIndex = 0;
            btnRemindme.Text = "Remindme";
            btnRemindme.UseVisualStyleBackColor = true;
            // 
            // btnNo
            // 
            btnNo.BackColor = Color.FromArgb(22, 71, 117);
            btnNo.Dock = DockStyle.Fill;
            btnNo.FlatStyle = FlatStyle.Flat;
            btnNo.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnNo.ForeColor = Color.FromArgb(234, 242, 248);
            btnNo.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnNo.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnNo.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnNo.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnNo.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnNo.Location = new Point(171, 95);
            btnNo.Margin = new Padding(4);
            btnNo.MinimumSize = new Size(64, 36);
            btnNo.Name = "btnNo";
            btnNo.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnNo.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnNo.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnNo.RadiusProperties.BorderColor = Color.Transparent;
            btnNo.RadiusProperties.BorderRadius = 10;
            btnNo.RadiusProperties.BorderSize = 0;
            btnNo.RightToLeft = RightToLeft.No;
            btnNo.Size = new Size(159, 62);
            btnNo.TabIndex = 0;
            btnNo.Text = "No";
            btnNo.UseVisualStyleBackColor = true;
            // 
            // StopPopupView
            // 
            AutoScaleDimensions = new SizeF(9F, 21F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(334, 161);
            Controls.Add(tlpMain);
            Margin = new Padding(4);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "StopPopupView";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "StopPopupView";
            TopMost = true;
            tlpMain.ResumeLayout(false);
            tlpMain.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private LeadTeamsTableLayoutPanel tlpMain;
        private LeadTeamsLabel lblHeader;
        private LeadTeamsButton btnRemindme;
        private LeadTeamsButton btnNo;
    }
}