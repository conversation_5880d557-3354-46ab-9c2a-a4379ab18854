﻿namespace LeadTeams.Client.Services
{
    public interface IUnplannedShutdownChecker
    {
        void DetectingUnplannedShutdowns();
    }

    internal class UnplannedShutdownChecker : IUnplannedShutdownChecker
    {
        private readonly ISession _session;
        private readonly IAttendanceLogService _attendanceLogService;
        private readonly IScreenShotsMonitoringService _screenShotsMonitoringService;
        private readonly IAttendanceLogger _attendanceLogger;

        public UnplannedShutdownChecker(ISession session, IAttendanceLogService attendanceLogService, IScreenShotsMonitoringService screenShotsMonitoringService, IAttendanceLogger attendanceLogger)
        {
            _session = session;
            _attendanceLogService = attendanceLogService;
            _screenShotsMonitoringService = screenShotsMonitoringService;
            _attendanceLogger = attendanceLogger;
        }

        public async void DetectingUnplannedShutdowns()
        {
            await Task.Run(async () =>
            {
                try
                {
                    _session.AppLogger.LogDebug($"Fetching last AttendanceLog record");
                    AttendanceLogModel? lastAttendanceLog = _attendanceLogService.GetLastAttendanceLog(_session.Employee.Id);

                    if (lastAttendanceLog is null)
                    {
                        _session.AppLogger.LogDebug($"There is no AttendanceLog records");
                        return;
                    }

                    if (lastAttendanceLog.AttendanceLogStatus == Enums.MonitoringStatus.Stopped.ToString())
                    {
                        _session.AppLogger.LogDebug($"Last record is Stopped");
                        return;
                    }

                    _session.AppLogger.LogDebug($"Fetching last ScreenShotsMonitoring record");
                    ScreenShotsMonitoringModel? lastScreenShot = _screenShotsMonitoringService.GetLastScreenShotsMonitoring(_session.Employee.Id);

                    if (lastScreenShot is null)
                    {
                        _session.AppLogger.LogDebug($"There is no ScreenShotsMonitoring records");
                        return;
                    }

                    // Adjust the context variable as needed
                    DateTime monitoringDate = lastScreenShot.ScreenShotsMonitoringDateTime;

                    using (EventLog log = new EventLog("System"))
                    {
                        // Filter entries directly during enumeration
                        _session.AppLogger.LogDebug($"Fetching records from windows event logs after or equals [{monitoringDate}]");
                        var entries = log.Entries.Cast<EventLogEntry>().Where(x => x.TimeGenerated >= monitoringDate || x.TimeWritten >= monitoringDate);

                        // Parallelize the filtering process
                        _session.AppLogger.LogDebug($"Filter logs on event id number 41 and 6008");
                        var filteredEntries = entries.Where(x => x.InstanceId == 41 || x.InstanceId == 6008)
                                                     .OrderBy(x => x.TimeGenerated).ToList();

                        if (filteredEntries == null || !filteredEntries.Any())
                        {
                            _session.AppLogger.LogDebug($"There is no logs records");
                            //// Clear filtered entries
                            //log.Clear();
                            return;
                        }

                        // Split filtered entries into respective categories
                        var entries41 = filteredEntries.Where(x => x.InstanceId == 41);
                        var entries6008 = filteredEntries.Where(x => x.InstanceId == 6008);
                        _session.AppLogger.LogDebug($"Found event id number 41 count [{entries41.Count()}] record");
                        _session.AppLogger.LogDebug($"Found event id number 6008 count [{entries6008.Count()}] record");

                        // Further processing if needed
                        if (entries6008.Any() || entries41.Any())
                        {
                            _session.AppLogger.LogDebug($"Create new AttendanceLog with last unplanned shutdown time");
                            lastAttendanceLog.Id = Ulid.NewUlid();
                            lastAttendanceLog.AttendanceLogDateTime = monitoringDate;
                            lastAttendanceLog.AttendanceLogStatus = Enums.MonitoringStatus.Stopped.ToString();
                            await _attendanceLogger.SaveAttendanceLogAsync(lastAttendanceLog.ToCreateDto());

                            //// Clear filtered entries
                            //log.Clear();
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    _session.AppLogger.LogError(ex, ex.Message);
                }
            });
        }

    }
}
