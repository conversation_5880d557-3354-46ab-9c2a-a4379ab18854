# 🖥️ Desktop Application Timezone Handling Guide

## 🎯 **Problem Solved**

You're absolutely right! `HttpContext` is always `null` in desktop applications because it only exists in web applications. Desktop apps need a different approach to get the current user's timezone.

## 🔧 **Desktop-Specific Solutions**

### **Option 1: Simple In-Memory User Context (Recommended for most cases)**

```csharp
// Program.cs or App.xaml.cs (WPF) or Program.cs (WinForms)
var services = new ServiceCollection();

// Register desktop timezone handling
services.AddDesktopTimezoneHandling<InMemoryDesktopUserContext>();

// Configure HttpClient with timezone handler
services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>(client =>
{
    client.BaseAddress = new Uri("https://api.leadteams.com/");
}).AddTimezoneHandler();

var serviceProvider = services.BuildServiceProvider();
```

**Usage in your desktop app:**
```csharp
// When user logs in
public async Task LoginAsync(string username, string password)
{
    var loginResult = await _authService.LoginAsync(username, password);
    if (loginResult.Success)
    {
        // Set current user with timezone
        DesktopUserSession.SetCurrentUser(
            _userContext, 
            loginResult.UserId, 
            loginResult.UserName,
            "America/New_York", // User's timezone
            "en-US"             // User's culture
        );
    }
}

// When user changes timezone in settings
public async Task UpdateTimezoneAsync(string newTimezone)
{
    await DesktopUserSession.UpdateUserTimezone(_userContext, _settingsService, newTimezone);
}

// When user logs out
public void Logout()
{
    DesktopUserSession.Logout(_userContext);
}
```

### **Option 2: Persistent Settings-Based**

```csharp
// Program.cs
var services = new ServiceCollection();

// Register settings-based timezone handling
services.AddDesktopSettingsTimezoneHandling<ConfigurationDesktopSettingsService>();

// Configure HttpClient
services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>(client =>
{
    client.BaseAddress = new Uri("https://api.leadteams.com/");
}).AddTimezoneHandler();

var serviceProvider = services.BuildServiceProvider();
```

**Usage:**
```csharp
// Save timezone to persistent settings
public void SaveUserTimezone(string timezone)
{
    _settingsService.SetSetting("UserTimezone", timezone);
    _settingsService.SetSetting("UserCulture", CultureInfo.CurrentCulture.Name);
    _settingsService.SaveSettings(); // Saves to file or registry
}
```

### **Option 3: Composite (User Context + Settings + System)**

```csharp
// Program.cs
var services = new ServiceCollection();

// Register composite timezone handling with fallbacks
services.AddDesktopCompositeTimezoneHandling<InMemoryDesktopUserContext, ConfigurationDesktopSettingsService>();

// Configure HttpClient
services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>(client =>
{
    client.BaseAddress = new Uri("https://api.leadteams.com/");
}).AddTimezoneHandler();

var serviceProvider = services.BuildServiceProvider();
```

## 🏗️ **Complete WPF Example**

### **1. App.xaml.cs Setup**
```csharp
public partial class App : Application
{
    public static IServiceProvider ServiceProvider { get; private set; }

    protected override void OnStartup(StartupEventArgs e)
    {
        var services = new ServiceCollection();
        
        // Register timezone handling for desktop
        services.AddDesktopCompositeTimezoneHandling<InMemoryDesktopUserContext, ConfigurationDesktopSettingsService>();
        
        // Register your API client
        services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>(client =>
        {
            client.BaseAddress = new Uri("https://api.leadteams.com/");
        }).AddTimezoneHandler(); // ← Automatically adds timezone headers
        
        // Register other services
        services.AddSingleton<MainWindow>();
        services.AddSingleton<LoginWindow>();
        
        ServiceProvider = services.BuildServiceProvider();
        
        // Show login window
        var loginWindow = ServiceProvider.GetRequiredService<LoginWindow>();
        loginWindow.Show();
        
        base.OnStartup(e);
    }
}
```

### **2. Login Window**
```csharp
public partial class LoginWindow : Window
{
    private readonly ILeadTeamsApiClient _apiClient;
    private readonly IDesktopUserContext _userContext;
    private readonly IDesktopSettingsService _settingsService;

    public LoginWindow(ILeadTeamsApiClient apiClient, IDesktopUserContext userContext, IDesktopSettingsService settingsService)
    {
        InitializeComponent();
        _apiClient = apiClient;
        _userContext = userContext;
        _settingsService = settingsService;
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Login API call - timezone headers automatically added
            var loginResult = await _apiClient.LoginAsync(UsernameTextBox.Text, PasswordBox.Password);
            
            if (loginResult.Success)
            {
                // Set current user with timezone
                DesktopUserSession.SetCurrentUser(
                    _userContext,
                    loginResult.UserId,
                    loginResult.UserName,
                    loginResult.UserTimezone ?? TimeZoneInfo.Local.Id,
                    loginResult.UserCulture ?? CultureInfo.CurrentCulture.Name
                );

                // Save timezone to settings for next login
                _settingsService.SetSetting("UserTimezone", loginResult.UserTimezone ?? TimeZoneInfo.Local.Id);
                _settingsService.SaveSettings();

                // Show main window
                var mainWindow = App.ServiceProvider.GetRequiredService<MainWindow>();
                mainWindow.Show();
                this.Close();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Login failed: {ex.Message}");
        }
    }
}
```

### **3. Main Window with Timezone Settings**
```csharp
public partial class MainWindow : Window
{
    private readonly ILeadTeamsApiClient _apiClient;
    private readonly IDesktopUserContext _userContext;
    private readonly IDesktopSettingsService _settingsService;

    public MainWindow(ILeadTeamsApiClient apiClient, IDesktopUserContext userContext, IDesktopSettingsService settingsService)
    {
        InitializeComponent();
        _apiClient = apiClient;
        _userContext = userContext;
        _settingsService = settingsService;
        
        LoadTimezones();
    }

    private void LoadTimezones()
    {
        // Populate timezone combobox
        TimezoneComboBox.ItemsSource = TimeZoneInfo.GetSystemTimeZones()
            .Select(tz => new { Id = tz.Id, DisplayName = tz.DisplayName })
            .ToList();
    }

    private async void GetAttendanceButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // API call - timezone headers automatically included
            var attendance = await _apiClient.GetAttendanceAsync(DateTime.Today.AddDays(-7), DateTime.Today);
            
            // Display attendance data
            AttendanceDataGrid.ItemsSource = attendance;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to load attendance: {ex.Message}");
        }
    }

    private async void TimezoneComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (TimezoneComboBox.SelectedValue is string selectedTimezone)
        {
            // Update user timezone
            await DesktopUserSession.UpdateUserTimezone(_userContext, _settingsService, selectedTimezone);
            
            MessageBox.Show($"Timezone updated to: {selectedTimezone}");
        }
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        // Clear user session
        DesktopUserSession.Logout(_userContext);
        
        // Show login window
        var loginWindow = App.ServiceProvider.GetRequiredService<LoginWindow>();
        loginWindow.Show();
        this.Close();
    }
}
```

### **4. API Client Usage**
```csharp
public class LeadTeamsApiClient : ILeadTeamsApiClient
{
    private readonly HttpClient _httpClient;

    public LeadTeamsApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient; // Already configured with TimeZoneDelegatingHandler
    }

    public async Task<AttendanceData[]> GetAttendanceAsync(DateTime startDate, DateTime endDate)
    {
        // This request will automatically include:
        // X-Timezone: America/New_York
        // X-Timezone-Offset: -05:00
        // X-Culture: en-US
        var response = await _httpClient.GetAsync($"api/attendance?start={startDate:yyyy-MM-dd}&end={endDate:yyyy-MM-dd}");
        response.EnsureSuccessStatusCode();
        
        return await response.Content.ReadFromJsonAsync<AttendanceData[]>() ?? Array.Empty<AttendanceData>();
    }

    public async Task<LoginResult> LoginAsync(string username, string password)
    {
        var loginRequest = new { Username = username, Password = password };
        var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginRequest);
        response.EnsureSuccessStatusCode();
        
        return await response.Content.ReadFromJsonAsync<LoginResult>() ?? new LoginResult();
    }
}
```

## 🎯 **Key Benefits for Desktop Apps**

1. **✅ No HttpContext Dependency**: Uses desktop-specific user context
2. **✅ Persistent Settings**: Timezone saved between app sessions
3. **✅ Automatic Headers**: All API calls include timezone information
4. **✅ Fallback Chain**: User → Settings → System timezone
5. **✅ Easy Integration**: Simple DI registration

## 🚀 **Quick Setup Checklist**

1. **✅ Register Services**: Use `AddDesktopCompositeTimezoneHandling<>`
2. **✅ Configure HttpClient**: Add `.AddTimezoneHandler()`
3. **✅ Set User on Login**: Call `DesktopUserSession.SetCurrentUser()`
4. **✅ Update on Settings Change**: Call `DesktopUserSession.UpdateUserTimezone()`
5. **✅ Clear on Logout**: Call `DesktopUserSession.Logout()`

## 🔍 **How It Works**

1. **User logs in** → Set timezone in `IDesktopUserContext`
2. **API call made** → `TimeZoneDelegatingHandler` gets timezone from context
3. **Headers added** → `X-Timezone`, `X-Timezone-Offset`, `X-Culture`
4. **API receives** → Timezone information available for processing

Your desktop application will now automatically include timezone information in all API requests! 🖥️✨
