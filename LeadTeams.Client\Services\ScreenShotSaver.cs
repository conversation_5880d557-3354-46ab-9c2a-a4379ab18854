﻿namespace LeadTeams.Client.Services
{
    public interface IScreenShotSaver
    {
        Task SaveScreenShotAsync(TaskModel? task, Enums.MonitoringStatus monitoringStatus);
    }

    internal class ScreenShotSaver : IScreenShotSaver
    {
        private readonly ISession _session;
        private readonly IScreenShotsMonitoringService _screenShotsMonitoringService;

        public ScreenShotSaver(ISession session, IScreenShotsMonitoringService screenShotsMonitoringService)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Start presenter");
            _session.AppLogger.LogInformation($"Setting variables");
            _screenShotsMonitoringService = screenShotsMonitoringService;
        }

        public async Task SaveScreenShotAsync(TaskModel? task, Enums.MonitoringStatus monitoringStatus)
        {
            try
            {
                _session.AppLogger.LogDebug($"Saving ScreenShotsMonitoring");
                ScreenShotEntity screenShot = CaptureScreenShot();
                var screenShotsMonitoring = new CreateScreenShotsMonitoringViewModel
                {
                    ScreenShotsMonitoringDateTime = screenShot.ScreenShotDateTime,
                    ScreenShotsMonitoringImageName = screenShot.ScreenShotName,
                    ScreenShotsMonitoringImagePath = SessionPath.SessionUserDataPath(),
                    ScreenShotsMonitoringImage = screenShot.ScreenShotBitmapArray,
                    ScreenShotsMonitoringStatus = monitoringStatus.ToString(),
                    OrganizationId = _session.Organization.Id,
                    EmployeeId = _session.Employee.Id,
                    TaskId = task?.Id,
                };
                await _screenShotsMonitoringService.AddAsync(screenShotsMonitoring);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving screenshot: {ex.Message}");
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }

        private ScreenShotEntity CaptureScreenShot()
        {
            try
            {
                ScreenShotEntity screenShot = new ScreenShotEntity();
                screenShot = screenShot.GerScreenShot(ScreenCapture.CaptureDesktop());
                string path = Path.Combine(SessionPath.SessionUserDataPath(), $"{_session.Employee.Id}_{DateTime.Now:yyyyMMdd_HHmmss}.png");

                using (Bitmap bitmap = screenShot.ScreenShotBitmap)
                {
                    bitmap.Save(path, ImageFormat.Png);
                }

                screenShot.ScreenShotName = Path.GetFileName(path);
                return screenShot;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error capturing screenshot: {ex.Message}");
                _session.AppLogger.LogError(ex, ex.Message);
                throw;
            }
        }
    }
}
