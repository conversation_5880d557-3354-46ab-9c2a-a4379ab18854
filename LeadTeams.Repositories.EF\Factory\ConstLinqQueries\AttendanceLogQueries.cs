namespace LeadTeams.Repositories.EF.Factory.ConstLinqQueries
{
    internal class AttendanceLogQueries
    {
        public static IQueryable<AttendanceLogViewModel> AttendanceLogViewQuery(ApplicationDBContext dbContext) => (from attendanceLog in dbContext.AttendanceLogs
                                                                                                                    join employee in dbContext.Employees on attendanceLog.EmployeeId equals employee.Id into employeeGroup
                                                                                                                    from employee in employeeGroup.DefaultIfEmpty()
                                                                                                                    join task in dbContext.Tasks on attendanceLog.TaskId equals task.Id into taskGroup
                                                                                                                    from task in taskGroup.DefaultIfEmpty()
                                                                                                                    select new AttendanceLogViewModel
                                                                                                                    {
                                                                                                                        Id = attendanceLog.Id,
                                                                                                                        AttendanceLogIPAddress = attendanceLog.AttendanceLogIPAddress,
                                                                                                                        AttendanceLogIPAddressType = attendanceLog.AttendanceLogIPAddressType,
                                                                                                                        AttendanceLogTimeZone = attendanceLog.AttendanceLogTimeZone,
                                                                                                                        AttendanceLogDateTime = attendanceLog.AttendanceLogDateTime,
                                                                                                                        AttendanceLogStatus = attendanceLog.AttendanceLogStatus,
                                                                                                                        EmployeeId = attendanceLog.EmployeeId,
                                                                                                                        EmployeeCustomID = employee.EmployeeCustomID,
                                                                                                                        EmployeeName = employee.EmployeeName,
                                                                                                                        EmployeeJobTitle = employee.EmployeeJobTitle,
                                                                                                                        TaskId = attendanceLog.TaskId,
                                                                                                                        TaskName = task.TaskName,
                                                                                                                        TaskDescription = task.TaskDescription,
                                                                                                                        TaskAssignDate = task.TaskAssignDate,
                                                                                                                        TaskDeadLineDate = task.TaskDeadLineDate,
                                                                                                                        TaskPriority = task.TaskPriority,
                                                                                                                        TaskStatus = task.TaskStatus,
                                                                                                                        OrganizationId = attendanceLog.OrganizationId,
                                                                                                                    });

        public static string GetAttendanceLogByDaily(Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string organizationCondition = GetOrganizationCondition(organizationId);
            string employeeCondition = GetEmployeeCondition(employeeId);
            string startDateCondition = GetStartDateCondition(startDate);
            string endDateCondition = GetEndDateCondition(endDate);

            return $@"
                WITH FilteredLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') AS FullDateTime,
                        AttendanceLogStatus,
                        AttendanceLogEmployeeShift
                    FROM AttendanceLog
                    WHERE AttendanceLogStatus IN ('WorkingOnTask', 'Stopped')
                    {organizationCondition}
                    {employeeCondition}
                    AND STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') >= '{startDateCondition}'
                    AND STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') <= '{endDateCondition}'
                ),
                RankedLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        FullDateTime,
                        AttendanceLogStatus,
                        LAG(AttendanceLogStatus) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS PrevStatus,
                        LAG(FullDateTime) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS PrevTime,
                        AttendanceLogEmployeeShift
                    FROM FilteredLogs
                ),
                GroupedLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        FullDateTime AS StartDateTime,
                        LEAD(FullDateTime) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS EndDateTime,
                        AttendanceLogEmployeeShift
                    FROM RankedLogs
                    WHERE AttendanceLogStatus = 'WorkingOnTask' 
                          AND (PrevStatus IS NULL OR PrevStatus != 'WorkingOnTask') 
                ),
                FinalResults AS (
                    SELECT 
                        g.EmployeeId,
                        g.OrganizationId,
                        g.StartDateTime,
                        MIN(r.FullDateTime) AS EndDateTime,
                        TIMESTAMPDIFF(SECOND, g.StartDateTime, MIN(r.FullDateTime)) AS TimeDifferenceSeconds,
                        g.AttendanceLogEmployeeShift
                    FROM GroupedLogs g
                    JOIN FilteredLogs r 
                        ON g.EmployeeId = r.EmployeeId 
                        AND g.OrganizationId = r.OrganizationId 
                        AND r.AttendanceLogStatus = 'Stopped'
                        AND r.FullDateTime > g.StartDateTime
                    GROUP BY g.EmployeeId, g.OrganizationId, g.StartDateTime, g.AttendanceLogEmployeeShift
                )
                SELECT 
                    f.OrganizationId,
                    f.EmployeeId,
                    e.EmployeeName,
                    f.StartDateTime,
                    MIN(f.EndDateTime) AS EndDateTime,
                    TIMESTAMPDIFF(SECOND, f.StartDateTime, MIN(f.EndDateTime)) AS TotalSeconds,
                    f.AttendanceLogEmployeeShift
                FROM FinalResults AS f 
                JOIN Employee e ON f.EmployeeId = e.Id AND f.OrganizationId = e.OrganizationId
                GROUP BY f.OrganizationId, f.EmployeeId, f.StartDateTime, f.AttendanceLogEmployeeShift
                ORDER BY f.OrganizationId, f.EmployeeId, f.StartDateTime;
            ";
        }

        public static string GetAttendanceLogByWeekly(Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string organizationCondition = GetOrganizationCondition(organizationId);
            string employeeCondition = GetEmployeeCondition(employeeId);
            string startDateCondition = GetStartDateCondition(startDate);
            string endDateCondition = GetEndDateCondition(endDate);

            return $@"
                WITH FilteredLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') AS FullDateTime,
                        AttendanceLogStatus,
                        AttendanceLogEmployeeShift
                    FROM AttendanceLog
                    WHERE AttendanceLogStatus IN ('WorkingOnTask', 'Stopped')
                    {organizationCondition}
                    {employeeCondition}
                    AND STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') >= '{startDateCondition}'
                    AND STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') <= '{endDateCondition}'
                ),
                RankedLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        FullDateTime,
                        AttendanceLogStatus,
                        LAG(AttendanceLogStatus) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS PrevStatus,
                        LAG(FullDateTime) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS PrevTime,
                        AttendanceLogEmployeeShift
                    FROM FilteredLogs
                ),
                GroupedLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        FullDateTime AS StartDateTime,
                        LEAD(FullDateTime) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS EndDateTime,
                        AttendanceLogEmployeeShift
                    FROM RankedLogs
                    WHERE AttendanceLogStatus = 'WorkingOnTask' 
                          AND (PrevStatus IS NULL OR PrevStatus != 'WorkingOnTask') 
                ),
                FinalResults AS (
                    SELECT 
                        g.EmployeeId,
                        g.OrganizationId,
                        g.StartDateTime,
                        MIN(r.FullDateTime) AS EndDateTime,
                        TIMESTAMPDIFF(SECOND, g.StartDateTime, MIN(r.FullDateTime)) AS TimeDifferenceSeconds,
                        g.AttendanceLogEmployeeShift
                    FROM GroupedLogs g
                    JOIN FilteredLogs r 
                        ON g.EmployeeId = r.EmployeeId 
                        AND g.OrganizationId = r.OrganizationId 
                        AND r.AttendanceLogStatus = 'Stopped'
                        AND r.FullDateTime > g.StartDateTime
                    GROUP BY g.EmployeeId, g.OrganizationId, g.StartDateTime, g.AttendanceLogEmployeeShift
                )
                SELECT 
                    f.OrganizationId,
                    f.EmployeeId,
                    e.EmployeeName,
                    YEAR(MIN(f.StartDateTime)) AS Year,
                    WEEK(MIN(f.StartDateTime), 3) AS WeekOfYear,
                    DATE_FORMAT(MIN(f.StartDateTime), '%Y-%m-%d') AS StartDateTime,
                    DATE_FORMAT(MAX(f.StartDateTime), '%Y-%m-%d') AS EndDateTime,
                    SUM(f.TimeDifferenceSeconds) AS TotalSeconds,
                    f.AttendanceLogEmployeeShift
                FROM FinalResults AS f
                JOIN Employee e ON f.EmployeeId = e.Id AND f.OrganizationId = e.OrganizationId
                GROUP BY f.OrganizationId, f.EmployeeId, YEAR(f.StartDateTime), WEEK(f.StartDateTime, 3), f.AttendanceLogEmployeeShift
                ORDER BY f.OrganizationId, f.EmployeeId, Year, WeekOfYear;
             ";
        }

        public static string GetAttendanceLogByMonthly(Ulid organizationId, Ulid? employeeId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            string organizationCondition = GetOrganizationCondition(organizationId);
            string employeeCondition = GetEmployeeCondition(employeeId);
            string startDateCondition = GetStartDateCondition(startDate);
            string endDateCondition = GetEndDateCondition(endDate);

            return $@"
                WITH FilteredLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') AS FullDateTime,
                        AttendanceLogStatus,
                        AttendanceLogEmployeeShift
                    FROM AttendanceLog
                    WHERE AttendanceLogStatus IN ('WorkingOnTask', 'Stopped')
                    {organizationCondition}
                    {employeeCondition}
                    AND STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') >= '{startDateCondition}'
                    AND STR_TO_DATE(AttendanceLogDateTime, '%Y-%m-%d %H:%i:%s') <= '{endDateCondition}'
                ),
                RankedLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        FullDateTime,
                        AttendanceLogStatus,
                        LAG(AttendanceLogStatus) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS PrevStatus,
                        LAG(FullDateTime) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS PrevTime,
                        AttendanceLogEmployeeShift
                    FROM FilteredLogs
                ),
                GroupedLogs AS (
                    SELECT 
                        EmployeeId,
                        OrganizationId,
                        FullDateTime AS StartDateTime,
                        LEAD(FullDateTime) OVER (PARTITION BY EmployeeId, OrganizationId ORDER BY FullDateTime) AS EndDateTime,
                        AttendanceLogEmployeeShift
                    FROM RankedLogs
                    WHERE AttendanceLogStatus = 'WorkingOnTask' 
                          AND (PrevStatus IS NULL OR PrevStatus != 'WorkingOnTask') 
                ),
                FinalResults AS (
                    SELECT 
                        g.EmployeeId,
                        g.OrganizationId,
                        g.StartDateTime,
                        MIN(r.FullDateTime) AS EndDateTime,
                        TIMESTAMPDIFF(SECOND, g.StartDateTime, MIN(r.FullDateTime)) AS TimeDifferenceSeconds,
                        g.AttendanceLogEmployeeShift
                    FROM GroupedLogs g
                    JOIN FilteredLogs r 
                        ON g.EmployeeId = r.EmployeeId 
                        AND g.OrganizationId = r.OrganizationId 
                        AND r.AttendanceLogStatus = 'Stopped'
                        AND r.FullDateTime > g.StartDateTime
                    GROUP BY g.EmployeeId, g.OrganizationId, g.StartDateTime, g.AttendanceLogEmployeeShift
                )
                SELECT 
                    f.OrganizationId,
                    f.EmployeeId,
                    e.EmployeeName,
                    YEAR(f.StartDateTime) AS Year,
                    MONTH(f.StartDateTime) AS Month,
                    DATE_FORMAT(MIN(f.StartDateTime), '%Y-%m-01') AS StartDateTime,
                    DATE_FORMAT(LAST_DAY(MAX(f.StartDateTime)), '%Y-%m-%d') AS EndDateTime,
                    SUM(f.TimeDifferenceSeconds) AS TotalSeconds,
                    f.AttendanceLogEmployeeShift
                FROM FinalResults AS f
                JOIN Employee e ON f.EmployeeId = e.Id AND f.OrganizationId = e.OrganizationId
                GROUP BY f.OrganizationId, f.EmployeeId, YEAR(f.StartDateTime), MONTH(f.StartDateTime), f.AttendanceLogEmployeeShift
                ORDER BY f.OrganizationId, f.EmployeeId, Year, Month;
             ";
        }

        private static string GetOrganizationCondition(Ulid? organizationId)
        {
            if (organizationId == null)
                return string.Empty;

            byte[] ulidBytes = organizationId.Value.ToByteArray();
            string ulidHex = BitConverter.ToString(ulidBytes).Replace("-", "").ToLower();
            string organizationCondition = organizationId != null ? $"AND OrganizationId = UNHEX('{ulidHex}')" : "";

            return organizationCondition;
        }

        private static string GetEmployeeCondition(Ulid? employeeId)
        {
            if (employeeId == null)
                return string.Empty;

            byte[] ulidBytes = employeeId.Value.ToByteArray();
            string ulidHex = BitConverter.ToString(ulidBytes).Replace("-", "").ToLower();
            string employeeCondition = employeeId != null ? $"AND EmployeeId = UNHEX('{ulidHex}')" : "";

            return employeeCondition;
        }

        private static string GetStartDateCondition(DateTime? from)
        {
            DateTime startDate = from ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month, 1);
            return startDate.ToString("yyyy-MM-dd HH:mm:ss");
        }

        private static string GetEndDateCondition(DateTime? to)
        {
            DateTime endDate = to ?? new DateTime(DateTime.Now.Date.Year, DateTime.Now.Date.Month + 1, 1).AddSeconds(-1);
            return endDate.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}
