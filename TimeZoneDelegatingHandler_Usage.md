# 🌍 TimeZoneDelegatingHandler - Complete Usage Guide

## 📋 **Overview**

The `TimeZoneDelegatingHandler` automatically adds timezone information to all outgoing HTTP requests, ensuring your API calls include the current user's timezone context.

## 🔧 **Implementation Options**

### **Option 1: System Timezone (Simplest)**
Uses the server's system timezone for all requests.

```csharp
// In Program.cs or Startup.cs
services.AddTimezoneHandling();

// Configure HttpClient
services.AddHttpClient("ApiClient", client =>
{
    client.BaseAddress = new Uri("https://api.leadteams.com/");
}).AddTimezoneHandler();
```

### **Option 2: Session/Claims Timezone**
Gets timezone from user session or JWT claims.

```csharp
// In Program.cs or Startup.cs
services.AddSessionTimezoneHandling();

// Configure HttpClient
services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>()
    .AddTimezoneHandler();
```

**Setting timezone in session:**
```csharp
// When user logs in or sets timezone preference
HttpContext.Session.SetString("UserTimezone", "America/New_York");
HttpContext.Session.SetString("UserCulture", "en-US");
```

**Setting timezone in JWT claims:**
```csharp
// When creating JWT token
var claims = new[]
{
    new Claim("sub", userId),
    new Claim("timezone", userTimezone), // e.g., "America/New_York"
    new Claim("culture", userCulture)    // e.g., "en-US"
};
```

### **Option 3: Database Timezone**
Gets timezone from user profile stored in database.

```csharp
// First, implement IUserService
public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;

    public UserService(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<UserProfile?> GetUserProfileAsync(string userId)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        return user != null ? new UserProfile 
        { 
            TimeZone = user.TimeZone, 
            Culture = user.Culture 
        } : null;
    }
}

// In Program.cs or Startup.cs
services.AddDatabaseTimezoneHandling<UserService>();

// Configure HttpClient
services.AddHttpClientWithTimezone<ILeadTeamsApiClient>();
```

### **Option 4: Header-Based Timezone**
Gets timezone from incoming request headers (for API-to-API calls).

```csharp
// In Program.cs or Startup.cs
services.AddHeaderTimezoneHandling();

// Configure HttpClient
services.AddHttpClientWithTimezone("InternalApi", client =>
{
    client.BaseAddress = new Uri("https://internal-api.leadteams.com/");
});
```

### **Option 5: Composite (Recommended)**
Tries multiple sources with fallback chain: Headers → Session → Database → System.

```csharp
// In Program.cs or Startup.cs
services.AddCompositeTimezoneHandling<UserService>();

// Configure HttpClient
services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>()
    .AddTimezoneHandler();
```

## 🎯 **Practical Examples**

### **Example 1: Web Application with User Preferences**

```csharp
// Program.cs
var builder = WebApplication.CreateBuilder(args);

// Add timezone handling with composite provider
builder.Services.AddCompositeTimezoneHandling<UserService>();

// Configure API client
builder.Services.AddHttpClient<ILeadTeamsApiClient, LeadTeamsApiClient>(client =>
{
    client.BaseAddress = new Uri("https://api.leadteams.com/");
}).AddTimezoneHandler();

var app = builder.Build();
```

```csharp
// UserController.cs
[HttpPost("set-timezone")]
public async Task<IActionResult> SetTimezone([FromBody] SetTimezoneRequest request)
{
    // Save to database
    await _userService.UpdateTimezoneAsync(User.GetUserId(), request.TimezoneId);
    
    // Update session for immediate effect
    HttpContext.Session.SetString("UserTimezone", request.TimezoneId);
    
    return Ok();
}
```

```csharp
// ApiClient usage - timezone automatically added
public class LeadTeamsApiClient : ILeadTeamsApiClient
{
    private readonly HttpClient _httpClient;

    public LeadTeamsApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<AttendanceData> GetAttendanceAsync(DateTime startDate, DateTime endDate)
    {
        // Timezone headers automatically added by TimeZoneDelegatingHandler
        var response = await _httpClient.GetAsync($"api/attendance?start={startDate:yyyy-MM-dd}&end={endDate:yyyy-MM-dd}");
        return await response.Content.ReadFromJsonAsync<AttendanceData>();
    }
}
```

### **Example 2: Mobile App with Device Timezone**

```csharp
// For mobile apps, create custom provider
public class DeviceTimeZoneProvider : ITimeZoneProvider
{
    public Task<TimezoneInfo?> GetCurrentTimezoneAsync()
    {
        // Get timezone from device
        var deviceTimezone = TimeZoneInfo.Local;
        
        return Task.FromResult<TimezoneInfo?>(new TimezoneInfo
        {
            Id = deviceTimezone.Id,
            Offset = FormatOffset(deviceTimezone.GetUtcOffset(DateTime.Now)),
            Culture = CultureInfo.CurrentCulture.Name
        });
    }

    private static string FormatOffset(TimeSpan offset)
    {
        var sign = offset >= TimeSpan.Zero ? "+" : "-";
        var absOffset = offset.Duration();
        return $"{sign}{absOffset:hh\\:mm}";
    }
}

// Register in DI
services.AddCustomTimezoneHandling<DeviceTimeZoneProvider>();
```

### **Example 3: Microservice Communication**

```csharp
// Service A calling Service B
public class OrderService
{
    private readonly HttpClient _httpClient;

    public OrderService(HttpClient httpClient)
    {
        _httpClient = httpClient; // Configured with TimeZoneDelegatingHandler
    }

    public async Task<Invoice> CreateInvoiceAsync(Order order)
    {
        // Request automatically includes timezone headers
        var response = await _httpClient.PostAsJsonAsync("api/invoices", order);
        return await response.Content.ReadFromJsonAsync<Invoice>();
    }
}

// Service B receives request with timezone context
[ApiController]
public class InvoicesController : ControllerBase
{
    [HttpPost]
    [Timezone] // Uses the timezone from headers sent by Service A
    public async Task<Invoice> CreateInvoice([FromBody] Order order)
    {
        var clientTimezone = HttpContext.GetClientTimezone();
        // Process with timezone context...
    }
}
```

## 🔍 **How It Works**

### **Request Flow:**
1. **HTTP Request Created** → `TimeZoneDelegatingHandler` intercepts
2. **Timezone Detection** → `ITimeZoneProvider` determines current timezone
3. **Header Injection** → Adds `X-Timezone`, `X-Timezone-Offset`, `X-Culture` headers
4. **Request Sent** → API receives request with timezone context
5. **API Processing** → `[Timezone]` attribute processes headers

### **Headers Added:**
```http
X-Timezone: America/New_York
X-Timezone-Offset: -05:00
X-Culture: en-US
```

## ⚙️ **Configuration Options**

```csharp
// Advanced configuration
services.Configure<TimezoneHandlingOptions>(options =>
{
    options.OverrideExistingHeaders = false;  // Don't override existing timezone headers
    options.IncludeCultureInfo = true;        // Include culture information
    options.LogFailures = true;               // Log timezone detection failures
    options.FallbackTimezoneId = "UTC";       // Fallback when detection fails
});
```

## 🧪 **Testing**

```csharp
[Test]
public async Task Should_Add_Timezone_Headers()
{
    // Arrange
    var mockProvider = new Mock<ITimeZoneProvider>();
    mockProvider.Setup(x => x.GetCurrentTimezoneAsync())
        .ReturnsAsync(new TimezoneInfo 
        { 
            Id = "America/New_York", 
            Offset = "-05:00" 
        });

    var handler = new TimeZoneDelegatingHandler(mockProvider.Object);
    var client = new HttpClient(handler);

    // Act
    await client.GetAsync("https://api.example.com/test");

    // Assert
    // Verify headers were added (you'll need to capture the request)
}
```

## 🚨 **Important Notes**

1. **Performance**: Timezone detection happens on every request - cache when possible
2. **Fallbacks**: Always provide fallback timezone providers
3. **Headers**: Don't override existing timezone headers unless explicitly configured
4. **Logging**: Enable logging for debugging timezone detection issues
5. **Testing**: Mock `ITimeZoneProvider` for unit tests

## 🎉 **Benefits**

- ✅ **Automatic**: No manual header management required
- ✅ **Flexible**: Multiple timezone sources with fallbacks
- ✅ **Consistent**: All API calls include timezone context
- ✅ **Testable**: Easy to mock and test
- ✅ **Configurable**: Supports various deployment scenarios
