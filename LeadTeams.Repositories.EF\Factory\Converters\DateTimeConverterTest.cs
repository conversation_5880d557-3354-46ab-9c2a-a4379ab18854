namespace LeadTeams.Repositories.EF.Factory.Converters
{
    /// <summary>
    /// Simple test class to verify DateTime conversion behavior.
    /// This demonstrates how the converters handle UTC storage and local time conversion.
    /// </summary>
    public static class DateTimeConverterTest
    {
        /// <summary>
        /// Tests the DateTime converter to ensure it properly converts to UTC for storage
        /// and back to local time when reading from the database.
        /// </summary>
        public static void TestDateTimeConversion()
        {
            var converter = new DateTimeConverter();

            // Test with a local DateTime
            var localDateTime = new DateTime(2024, 1, 15, 14, 30, 0, DateTimeKind.Local);
            Console.WriteLine($"Original Local DateTime: {localDateTime} (Kind: {localDateTime.Kind})");

            // Convert to provider (database) format - should be UTC
            var utcForStorage = converter.ConvertToProvider(localDateTime);
            Console.WriteLine($"Converted to UTC for storage: {utcForStorage} (Kind: {utcForStorage.Kind})");

            // Convert back from provider (database) format - should be local time
            var backToLocal = converter.ConvertFromProvider(utcForStorage);
            Console.WriteLine($"Converted back to Local: {backToLocal} (Kind: {backToLocal.Kind})");

            Console.WriteLine();

            // Test with UTC DateTime
            var utcDateTime = new DateTime(2024, 1, 15, 14, 30, 0, DateTimeKind.Utc);
            Console.WriteLine($"Original UTC DateTime: {utcDateTime} (Kind: {utcDateTime.Kind})");

            var utcForStorage2 = converter.ConvertToProvider(utcDateTime);
            Console.WriteLine($"Converted to UTC for storage: {utcForStorage2} (Kind: {utcForStorage2.Kind})");

            var backToLocal2 = converter.ConvertFromProvider(utcForStorage2);
            Console.WriteLine($"Converted back to Local: {backToLocal2} (Kind: {backToLocal2.Kind})");
        }

        /// <summary>
        /// Tests the DateTimeOffset converter to ensure it properly handles timezone conversions.
        /// </summary>
        public static void TestDateTimeOffsetConversion()
        {
            var converter = new DateTimeOffsetConverter();

            // Test with a DateTimeOffset
            var dateTimeOffset = new DateTimeOffset(2024, 1, 15, 14, 30, 0, TimeSpan.FromHours(2)); // UTC+2
            Console.WriteLine($"Original DateTimeOffset: {dateTimeOffset}");

            // Convert to provider (database) format - should be UTC DateTime
            var utcForStorage = converter.ConvertToProvider(dateTimeOffset);
            Console.WriteLine($"Converted to UTC DateTime for storage: {utcForStorage} (Kind: {utcForStorage.Kind})");

            // Convert back from provider (database) format - should be local time
            var backToLocal = converter.ConvertFromProvider(utcForStorage);
            Console.WriteLine($"Converted back to Local DateTime: {backToLocal} (Kind: {backToLocal.Kind})");
        }
    }
}
