using System.Configuration;
using System.Globalization;

namespace LeadTeams.PermissionAndSession.Authentication
{
    /// <summary>
    /// Simple in-memory implementation of IDesktopUserContext for desktop applications.
    /// Stores current user information in memory during application session.
    /// </summary>
    public class InMemoryDesktopUserContext : IDesktopUserContext
    {
        private DesktopUser? _currentUser;
        private readonly object _lock = new object();

        public Task<DesktopUser?> GetCurrentUserAsync()
        {
            lock (_lock)
            {
                return Task.FromResult(_currentUser);
            }
        }

        public void SetCurrentUser(DesktopUser user)
        {
            lock (_lock)
            {
                _currentUser = user;
            }
        }

        public void ClearCurrentUser()
        {
            lock (_lock)
            {
                _currentUser = null;
            }
        }
    }

    /// <summary>
    /// App.config/appsettings.json based implementation of IDesktopSettingsService.
    /// Stores timezone settings in application configuration.
    /// </summary>
    public class ConfigurationDesktopSettingsService : IDesktopSettingsService
    {
        private readonly Dictionary<string, string> _settings = new();
        private readonly string _configFilePath;

        public ConfigurationDesktopSettingsService(string? configFilePath = null)
        {
            _configFilePath = configFilePath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "LeadTeams",
                "user-settings.json");
            
            LoadSettings();
        }

        public string? GetSetting(string key)
        {
            _settings.TryGetValue(key, out var value);
            return value;
        }

        public void SetSetting(string key, string value)
        {
            _settings[key] = value;
        }

        public void SaveSettings()
        {
            try
            {
                var directory = Path.GetDirectoryName(_configFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = System.Text.Json.JsonSerializer.Serialize(_settings, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save settings: {ex.Message}");
            }
        }

        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    var settings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(json);
                    if (settings != null)
                    {
                        foreach (var kvp in settings)
                        {
                            _settings[kvp.Key] = kvp.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load settings: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Windows Registry based implementation of IDesktopSettingsService.
    /// Stores timezone settings in Windows Registry.
    /// </summary>
    public class RegistryDesktopSettingsService : IDesktopSettingsService
    {
        private const string RegistryKeyPath = @"SOFTWARE\LeadTeams\UserSettings";
        private readonly Microsoft.Win32.RegistryKey? _registryKey;

        public RegistryDesktopSettingsService()
        {
            try
            {
                _registryKey = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(RegistryKeyPath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to access registry: {ex.Message}");
            }
        }

        public string? GetSetting(string key)
        {
            try
            {
                return _registryKey?.GetValue(key)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        public void SetSetting(string key, string value)
        {
            try
            {
                _registryKey?.SetValue(key, value);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to set registry value: {ex.Message}");
            }
        }

        public void SaveSettings()
        {
            // Registry values are saved immediately, no explicit save needed
        }

        public void Dispose()
        {
            _registryKey?.Dispose();
        }
    }

    /// <summary>
    /// Database-backed implementation of IDesktopUserContext.
    /// Retrieves user information from database and caches it locally.
    /// </summary>
    public class DatabaseDesktopUserContext : IDesktopUserContext
    {
        private readonly IUserServiceProvider _userService;
        private DesktopUser? _cachedUser;
        private DateTime? _lastCacheTime;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30);
        private readonly object _lock = new object();

        public DatabaseDesktopUserContext(IUserServiceProvider userService)
        {
            _userService = userService;
        }

        public async Task<DesktopUser?> GetCurrentUserAsync()
        {
            lock (_lock)
            {
                // Return cached user if still valid
                if (_cachedUser != null && _lastCacheTime.HasValue && 
                    DateTime.Now - _lastCacheTime.Value < _cacheExpiry)
                {
                    return _cachedUser;
                }
            }

            // Refresh from database
            try
            {
                if (_cachedUser?.UserId != null)
                {
                    var userProfile = await _userService.GetUserProfileAsync(_cachedUser.UserId);
                    if (userProfile != null)
                    {
                        lock (_lock)
                        {
                            _cachedUser.TimeZone = userProfile.TimeZone;
                            _cachedUser.Culture = userProfile.Culture;
                            _lastCacheTime = DateTime.Now;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to refresh user from database: {ex.Message}");
            }

            lock (_lock)
            {
                return _cachedUser;
            }
        }

        public void SetCurrentUser(DesktopUser user)
        {
            lock (_lock)
            {
                _cachedUser = user;
                _lastCacheTime = DateTime.Now;
            }
        }

        public void ClearCurrentUser()
        {
            lock (_lock)
            {
                _cachedUser = null;
                _lastCacheTime = null;
            }
        }
    }

    /// <summary>
    /// Helper class for managing desktop user sessions.
    /// </summary>
    public static class DesktopUserSession
    {
        /// <summary>
        /// Sets the current user with timezone information.
        /// Call this after successful login.
        /// </summary>
        public static void SetCurrentUser(IDesktopUserContext userContext, string userId, string userName, string? timeZone = null, string? culture = null)
        {
            var user = new DesktopUser
            {
                UserId = userId,
                UserName = userName,
                TimeZone = timeZone ?? TimeZoneInfo.Local.Id,
                Culture = culture ?? CultureInfo.CurrentCulture.Name,
                LastLogin = DateTime.Now
            };

            userContext.SetCurrentUser(user);
        }

        /// <summary>
        /// Updates the current user's timezone preference.
        /// Call this when user changes timezone settings.
        /// </summary>
        public static async Task UpdateUserTimezone(IDesktopUserContext userContext, IDesktopSettingsService? settingsService, string timeZone, string? culture = null)
        {
            var currentUser = await userContext.GetCurrentUserAsync();
            if (currentUser != null)
            {
                currentUser.TimeZone = timeZone;
                if (culture != null)
                {
                    currentUser.Culture = culture;
                }
                userContext.SetCurrentUser(currentUser);
            }

            // Also save to settings for persistence
            settingsService?.SetSetting("UserTimezone", timeZone);
            if (culture != null)
            {
                settingsService?.SetSetting("UserCulture", culture);
            }
            settingsService?.SaveSettings();
        }

        /// <summary>
        /// Clears the current user session.
        /// Call this on logout.
        /// </summary>
        public static void Logout(IDesktopUserContext userContext)
        {
            userContext.ClearCurrentUser();
        }
    }
}
