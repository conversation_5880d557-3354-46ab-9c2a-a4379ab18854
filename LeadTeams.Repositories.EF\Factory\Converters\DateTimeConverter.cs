using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace LeadTeams.Repositories.EF.Factory.Converters
{
    /// <summary>
    /// Converts DateTime values to UTC for database storage and back to local time when reading.
    /// This ensures all DateTime values are stored consistently in UTC in the database.
    /// </summary>
    public class DateTimeConverter : ValueConverter<DateTime, DateTime>
    {
        public DateTimeConverter() : this(null)
        {
        }

        public DateTimeConverter(ConverterMappingHints? mappingHints)
            : base(
                    convertToProviderExpression: x => x.ToUniversalTime(),
                    convertFromProviderExpression: x => DateTime.SpecifyKind(x, DateTimeKind.Utc).ToLocalTime())
        {
        }
    }

    /// <summary>
    /// Converts DateTimeOffset values to UTC DateTime for database storage and back to local time when reading.
    /// This provides consistent timezone handling for DateTimeOffset properties.
    /// </summary>
    public class DateTimeOffsetConverter : ValueConverter<DateTimeOffset, DateTime>
    {
        public DateTimeOffsetConverter() : this(null)
        {
        }

        public DateTimeOffsetConverter(ConverterMappingHints? mappingHints)
            : base(
                    convertToProviderExpression: x => x.UtcDateTime,
                    convertFromProviderExpression: x => DateTime.SpecifyKind(x, DateTimeKind.Utc).ToLocalTime())
        {
        }
    }
}
