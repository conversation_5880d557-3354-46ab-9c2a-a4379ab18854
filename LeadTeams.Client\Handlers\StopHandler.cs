﻿namespace LeadTeams.Client.Handlers
{
    internal class StopHandler
    {
        private readonly ISession _session;
        private readonly IEmployeeTrackerView _view;
        private readonly IServices _services;
        private readonly IEmployeeService _employeeService;
        private readonly IAttendanceLogger _attendanceLogger;
        private readonly IScreenShotSaver _screenShotSaver;
        private readonly IPopupService _popupService;
        private readonly WorkHandler _workHandler;
        private Enums.StopPopUpResult _stopPopUpResult;
        private Enums.SnoozePopUpResult _snoozePopUpResult;
        private System.Timers.Timer _reminderTimer;
        private int _checkingSnoozeCounter;
        private int _maxSnooze;

        internal StopHandler(ISession session, IEmployeeTrackerView view, IServices services, IEmployeeService employeeService, IAttendanceLogger attendanceLogger, IScreenShotSaver screenShotSaver, IPopupService popupService, WorkHandler workHandler)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Start presenter");
            _session.AppLogger.LogInformation($"Setting variables");
            _view = view;
            _services = services;
            _employeeService = employeeService;
            _attendanceLogger = attendanceLogger;
            _screenShotSaver = screenShotSaver;
            _popupService = popupService;
            _workHandler = workHandler;
            _session.AppLogger.LogInformation($"Setting configs");
            _reminderTimer = new System.Timers.Timer(1000);

            //Subscribe event handler methods to view events
            _session.AppLogger.LogInformation($"Linking events");
            _reminderTimer.Elapsed += StopReminderHandlerAsync;
        }

        internal async Task StopWorking()
        {
            _session.AppLogger.LogInformation($"Start stopping process");
            if (_view.MonitoringStatus == Enums.MonitoringStatus.Stopped)
                return;

            _session.AppLogger.LogInformation($"Set MonitoringStatus to Stopped");
            _view.MonitoringStatus = Enums.MonitoringStatus.Stopped;
            await StopWorking(_view.MonitoringStatus);
            _session.AppLogger.LogInformation($"Update WorkedTime to last datetime");
            _services.WorkedTime = _employeeService.GetEmployeeWorkingDayTime(_session.Organization.Id, _session.Employee.Id);
            _services.UpdateTimers();
            await StopWorkingHandler();
        }

        private async Task StopWorkingHandler()
        {
            _stopPopUpResult = _popupService.ShowStopPopUp();
            _session.AppLogger.LogDebug($"Handle ConnectionStatus action [{_stopPopUpResult}]");

            switch (_stopPopUpResult)
            {
                case Enums.StopPopUpResult.Remindme:
                    await StopReminderHandler();
                    break;
                case Enums.StopPopUpResult.No:
                    break;
            }
        }

        private async Task StopReminderHandler()
        {
            try
            {
                var SnoozeDialogResult = _popupService.ShowSnoozePopUp();
                _session.AppLogger.LogDebug($"Handle SnoozeDialogResult action [{SnoozeDialogResult.Item1},{SnoozeDialogResult.Item2}]");
                _snoozePopUpResult = SnoozeDialogResult.Item1;
                switch (_snoozePopUpResult)
                {
                    case Enums.SnoozePopUpResult.Snooze:
                        int? SnoozeTime = SnoozeDialogResult.Item2;
                        if (SnoozeTime == null)
                            return;
                        _maxSnooze = SnoozeTime.Value;
                        _session.AppLogger.LogDebug($"Start ReminderTimer");
                        _reminderTimer.Start();
                        break;
                    case Enums.SnoozePopUpResult.StartWork:
                        _session.AppLogger.LogDebug($"Start working again");
                        await _workHandler.StartWorking();
                        break;
                    case Enums.SnoozePopUpResult.Shutdown:
                        _session.AppLogger.LogDebug($"Shutdown the application");
                        _view.CloseView();
                        break;
                }
            }
            catch (Exception ex)
            {
                _view.Message = ex.Message;
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }

        private void StopReminderHandlerAsync(object? sender, System.Timers.ElapsedEventArgs e)
        {
            _ = HandleStopReminderAsync();
        }

        private async Task HandleStopReminderAsync()
        {
            if (_view.MonitoringStatus == Enums.MonitoringStatus.WorkingOnTask)
            {
                _reminderTimer.Stop();
                _checkingSnoozeCounter = 0;
                _view.Test5 = $"{_checkingSnoozeCounter} - {_maxSnooze}";
                return;
            }
            _checkingSnoozeCounter++;
            _view.Test5 = $"{_checkingSnoozeCounter} - {_maxSnooze}";
            if (_checkingSnoozeCounter >= _maxSnooze)
            {
                _reminderTimer.Stop();
                _checkingSnoozeCounter = 0;
                await StopWorkingHandler();
            }
        }

        internal async Task StopWorking(Enums.MonitoringStatus monitoringStatus)
        {
            _view.IsWorking = false;
            _services.StopwatchWorkingTime.Stop();
            _services.StopwatchOverTime.Stop();
            await _attendanceLogger.SaveAttendanceLogAsync(_view.TaskModel, monitoringStatus);
            await _screenShotSaver.SaveScreenShotAsync(_view.TaskModel, monitoringStatus);
        }
    }
}
