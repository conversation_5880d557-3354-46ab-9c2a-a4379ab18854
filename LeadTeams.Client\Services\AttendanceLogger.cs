﻿namespace LeadTeams.Client.Services
{
    public interface IAttendanceLogger
    {
        Task SaveAttendanceLogAsync(TaskModel? task, Enums.MonitoringStatus monitoringStatus);
        Task SaveAttendanceLogAsync(CreateAttendanceLogViewModel attendanceLog);
    }

    internal class AttendanceLogger : IAttendanceLogger
    {
        private readonly ISession _session;
        private readonly IAttendanceLogService _attendanceLogService;

        public AttendanceLogger(ISession session, IAttendanceLogService attendanceLogService)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Start presenter");
            _session.AppLogger.LogInformation($"Setting variables");
            _attendanceLogService = attendanceLogService;
        }

        public async Task SaveAttendanceLogAsync(TaskModel? task, Enums.MonitoringStatus monitoringStatus)
        {
            try
            {
                _session.AppLogger.LogDebug($"Saving AttendanceLog");

                if (task == null)
                    return;

                Ulid? taskId = task.Id;

                var shiftDto = ShiftDto.ToShiftDto(_session.Employee.Shift);

                var attendanceLogModel = new CreateAttendanceLogViewModel
                {
                    AttendanceLogIPAddress = IPAddressHelper.GetIPAddress.IP,
                    AttendanceLogIPAddressType = IPAddressHelper.GetIPAddress.IPType.ToString(),
                    AttendanceLogTimeZone = TimeZoneInfo.Local.StandardName,
                    AttendanceLogDateTime = DateTime.Now,
                    AttendanceLogStatus = monitoringStatus.ToString(),
                    OrganizationId = _session.Organization.Id,
                    EmployeeId = _session.Employee.Id,
                    TaskId = taskId,
                    AttendanceLogEmployeeShift = Shared.Helper.JsonUtilities.SaveToJsonString(shiftDto),
                };

                var validator = new BaseValidation();
                validator.Validate(attendanceLogModel);

                var result = await _attendanceLogService.AddAsync(attendanceLogModel);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }

        public async Task SaveAttendanceLogAsync(CreateAttendanceLogViewModel attendanceLog)
        {
            try
            {
                _session.AppLogger.LogDebug($"Saving AttendanceLog");
                await _attendanceLogService.AddAsync(attendanceLog);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                _session.AppLogger.LogError(ex, ex.Message);
            }
        }
    }
}
