﻿namespace LeadTeams.Services.API.DI
{
    public static class InjectServiceLayerServices
    {
        public static IServiceCollection InjectServiceServices(this IServiceCollection services)
        {
            Utilities.Initialize(Assembly.GetExecutingAssembly());

            services.InjectPermissionAndSessionServices();

            services.AddHttpClient<IDataService, ApiService>("LeadTeams", (servicess, client) =>
            {
                client.BaseAddress = new Uri("http://api-leadteams-test.runasp.net/api/");
#if DEBUG
                client.BaseAddress = new Uri("https://localhost:7038/api/");
#endif
                var authenticationValidationService = servicess.GetRequiredService<IAuthenticationValidationService>();
                string token = authenticationValidationService.GetStringToken();
                if (!string.IsNullOrEmpty(token))
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(AuthenticationDefaults.AuthenticationScheme, token);
            })
            .AddHttpMessageHandler<TokenRefreshDelegatingHandler>()
            .AddHttpMessageHandler<TimeZoneDelegatingHandler>();

            services.AddHttpContextAccessor();
            services.AddSingleton<IDataService, ApiService>();
            services.AddHostedService<ApiMonitorService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IAllowanceService, AllowanceService>();
            services.AddScoped<IAskLeaveService, AskLeaveService>();
            services.AddScoped<IAttendanceLogService, AttendanceLogService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IManagementTeamService, ManagementTeamService>();
            services.AddScoped<IMeetingService, MeetingService>();
            services.AddScoped<IOrganizationNewsService, OrganizationNewsService>();
            services.AddScoped<IProjectService, ProjectService>();
            services.AddScoped<IScreensAccessProfileService, ScreensAccessProfileService>();
            services.AddScoped<ISettingService, SettingService>();
            services.AddScoped<IScreenShotsMonitoringService, ScreenShotsMonitoringService>();
            services.AddScoped<IShiftService, ShiftService>();
            services.AddScoped<ITaskService, TaskService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAttendanceService, AttendanceService>();
            services.AddScoped<IPayrollService, PayrollService>();

            // Register token refresh services
            services.AddSingleton<ITokenRefreshService, TokenRefreshService>();
            services.AddTransient<TokenRefreshDelegatingHandler>();
            services.AddTransient<TimeZoneDelegatingHandler>();

            return services;
        }
    }
}
