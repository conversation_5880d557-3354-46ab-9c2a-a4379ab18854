﻿namespace LeadTeams.Repositories.EF.Repository
{
    internal class ScreenShotsMonitoringRepository : LeadTeamsBaseRepository<ScreenShotsMonitoringModel, ScreenShotsMonitoringViewModel>, IScreenShotsMonitoringRepository<ScreenShotsMonitoringModel, ScreenShotsMonitoringViewModel>
    {
        public ScreenShotsMonitoringRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<ScreenShotsMonitoringModel, string?>>[] NamesOfSearch => [x => x.ScreenShotsMonitoringStatus];
        protected override IQueryable<ScreenShotsMonitoringViewModel> ViewQueryable => ScreenShotsMonitoringQueries.ScreenShotsMonitoringViewQuery(_context);
        protected override string ViewQuery => ScreenShotsMonitoringQueries.ScreenShotsMonitoringViewQuery(_context).ToQueryString();
        protected override Expression<Func<ScreenShotsMonitoringViewModel, object>> OrderByColumn => x => x.Id;

        public override async Task<PaginationList<ScreenShotsMonitoringViewModel>> GetAllViewAsync(PaginationSpecifications<ScreenShotsMonitoringViewModel> paginationSpecifications)
        {
            paginationSpecifications.OrderByType = OrderByType.Descending;

            var result = await base.GetAllViewAsync(paginationSpecifications);
            return result;
        }

        public ScreenShotsMonitoringModel? GetLastScreenShotsMonitoring(Ulid employeeId)
        {
            RepositorySpecifications<ScreenShotsMonitoringModel> repositorySpecifications = new RepositorySpecifications<ScreenShotsMonitoringModel>()
            {
                SearchValue = x => x.EmployeeId == employeeId,
            };

            IQueryable<ScreenShotsMonitoringModel> query = GetQueryable(repositorySpecifications);
            ScreenShotsMonitoringModel? screenShotsMonitoring = query.OrderBy(x => x.Id).LastOrDefault();
            return screenShotsMonitoring;
        }
    }
}
