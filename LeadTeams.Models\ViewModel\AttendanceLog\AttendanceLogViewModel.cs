﻿namespace LeadTeams.Models.ViewModel.AttendanceLog
{
    public class AttendanceLogViewModel : BaseIdentityModel
    {
        [Browsable(false)]
        public Ulid OrganizationId { get; set; }

        #region AttendanceLog
        [DisplayName("IP Address")]
        public string AttendanceLogIPAddress { get; set; }
        [DisplayName("IP Address Type")]
        public string AttendanceLogIPAddressType { get; set; }
        [DisplayName("TimeZone")]
        public string AttendanceLogTimeZone { get; set; }
        [DisplayName("Log DateTime")]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime AttendanceLogDateTime { get; set; }
        [DisplayName("Log Status")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(Enums.MonitoringStatus))]
        public string AttendanceLogStatus { get; set; }
        #endregion

        #region Employee
        [DisplayName("Employee ID")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(EmployeeModel))]
        public Ulid EmployeeId { get; set; }
        [DisplayName("Employee CustomI ID")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string EmployeeCustomID { get; set; }
        [DisplayName("Employee Name")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string EmployeeName { get; set; }
        [DisplayName("Job Title")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string? EmployeeJobTitle { get; set; }
        #endregion

        #region Task
        [DisplayName("Task Id")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(TaskModel))]
        public Ulid? TaskId { get; set; }
        [DisplayName("Task Name")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string TaskName { get; set; }
        [DisplayName("Task Description")]
        public string? TaskDescription { get; set; }
        [DisplayName("Task Assign Date")]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime TaskAssignDate { get; set; }
        [DisplayName("Task DeadLine Date")]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime TaskDeadLineDate { get; set; }
        [DisplayName("Task Priority")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(TaskPriorityEnumeration))]
        public string TaskPriority { get; set; }
        [DisplayName("Task Status")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(TaskStatusEnumeration))]
        public string TaskStatus { get; set; }
        #endregion
    }
}
