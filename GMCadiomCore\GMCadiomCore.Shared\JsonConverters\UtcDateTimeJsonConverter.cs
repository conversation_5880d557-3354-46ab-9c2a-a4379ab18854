using System.Text.Json;
using System.Text.Json.Serialization;

namespace GMCadiomCore.Shared.JsonConverters
{
    /// <summary>
    /// JSON converter that ensures all DateTime values are serialized/deserialized as UTC for API consistency.
    /// This converter handles global API clients by standardizing all DateTime communication to UTC.
    /// </summary>
    public class UtcDateTimeJsonConverter : JsonConverter<DateTime>
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                var dateTimeString = reader.GetString();
                if (string.IsNullOrEmpty(dateTimeString))
                    return DateTime.MinValue;

                // Try to parse the DateTime string
                if (DateTime.TryParse(dateTimeString, out var dateTime))
                {
                    // Ensure the DateTime is treated as UTC
                    return dateTime.Kind switch
                    {
                        DateTimeKind.Utc => dateTime,
                        DateTimeKind.Local => dateTime.ToUniversalTime(),
                        DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                        _ => dateTime
                    };
                }
            }

            throw new JsonException($"Unable to convert \"{reader.GetString()}\" to DateTime.");
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            // Always write DateTime as UTC with 'Z' suffix for API consistency
            var utcDateTime = value.Kind switch
            {
                DateTimeKind.Utc => value,
                DateTimeKind.Local => value.ToUniversalTime(),
                DateTimeKind.Unspecified => DateTime.SpecifyKind(value, DateTimeKind.Utc),
                _ => value
            };

            writer.WriteStringValue(utcDateTime.ToString(DateTimeFormat));
        }
    }

    /// <summary>
    /// JSON converter for nullable DateTime values with UTC handling.
    /// </summary>
    public class UtcNullableDateTimeJsonConverter : JsonConverter<DateTime?>
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
                return null;

            if (reader.TokenType == JsonTokenType.String)
            {
                var dateTimeString = reader.GetString();
                if (string.IsNullOrEmpty(dateTimeString))
                    return null;

                if (DateTime.TryParse(dateTimeString, out var dateTime))
                {
                    return dateTime.Kind switch
                    {
                        DateTimeKind.Utc => dateTime,
                        DateTimeKind.Local => dateTime.ToUniversalTime(),
                        DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                        _ => dateTime
                    };
                }
            }

            throw new JsonException($"Unable to convert \"{reader.GetString()}\" to DateTime?.");
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNullValue();
                return;
            }

            var utcDateTime = value.Value.Kind switch
            {
                DateTimeKind.Utc => value.Value,
                DateTimeKind.Local => value.Value.ToUniversalTime(),
                DateTimeKind.Unspecified => DateTime.SpecifyKind(value.Value, DateTimeKind.Utc),
                _ => value.Value
            };

            writer.WriteStringValue(utcDateTime.ToString(DateTimeFormat));
        }
    }

    /// <summary>
    /// JSON converter for DateTimeOffset values ensuring UTC serialization.
    /// </summary>
    public class UtcDateTimeOffsetJsonConverter : JsonConverter<DateTimeOffset>
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override DateTimeOffset Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                var dateTimeString = reader.GetString();
                if (string.IsNullOrEmpty(dateTimeString))
                    return DateTimeOffset.MinValue;

                if (DateTimeOffset.TryParse(dateTimeString, out var dateTimeOffset))
                {
                    // Convert to UTC for consistency
                    return dateTimeOffset.ToUniversalTime();
                }
            }

            throw new JsonException($"Unable to convert \"{reader.GetString()}\" to DateTimeOffset.");
        }

        public override void Write(Utf8JsonWriter writer, DateTimeOffset value, JsonSerializerOptions options)
        {
            // Always write as UTC
            writer.WriteStringValue(value.ToUniversalTime().ToString(DateTimeFormat));
        }
    }
}
