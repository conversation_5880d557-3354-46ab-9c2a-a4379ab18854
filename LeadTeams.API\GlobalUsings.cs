﻿global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.ModelValidation.Base;
global using GMCadiomCore.Models.ResultPattern;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Shared.Helper;
global using GMCadiomCore.Shared.JsonConverters;
global using LeadTeams.API.Attributes;
global using LeadTeams.API.Extensions;
global using LeadTeams.API.Hubs;
global using LeadTeams.API.Middlewares;
global using LeadTeams.API.Models.Timezone;
global using LeadTeams.Models.Model;
global using LeadTeams.Models.ModelDTO;
global using LeadTeams.Models.ModelDTO.Authentication;
global using LeadTeams.Models.ViewModel.Allowance;
global using LeadTeams.Models.ViewModel.AskLeave;
global using LeadTeams.Models.ViewModel.AttendanceLog;
global using LeadTeams.Models.ViewModel.BaseModels;
global using LeadTeams.Models.ViewModel.Employee;
global using LeadTeams.Models.ViewModel.ManagementTeam;
global using LeadTeams.Models.ViewModel.Meeting;
global using LeadTeams.Models.ViewModel.OrganizationNews;
global using LeadTeams.Models.ViewModel.Project;
global using LeadTeams.Models.ViewModel.ScreensAccessProfile;
global using LeadTeams.Models.ViewModel.ScreenShotsMonitoring;
global using LeadTeams.Models.ViewModel.Shift;
global using LeadTeams.Models.ViewModel.Task;
global using LeadTeams.Models.ViewModel.User;
global using LeadTeams.PermissionAndSession;
global using LeadTeams.PermissionAndSession.Authentication;
global using LeadTeams.PermissionAndSession.DI;
global using LeadTeams.PermissionAndSession.Session;
global using LeadTeams.Repositories.Core.IFactory;
global using LeadTeams.Services.Core.Authentication;
global using LeadTeams.Services.Core.BaseService;
global using LeadTeams.Services.Core.Business;
global using LeadTeams.Services.Core.Reports;
global using LeadTeams.Services.Server.DI;
global using LeadTeams.Shared.Helper;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.ModelBinding;
global using Microsoft.AspNetCore.SignalR;
global using Microsoft.OpenApi.Models;
global using Serilog;
global using SignalRChat.Core.Data.Repositories;
global using SignalRChat.Core.DI;
global using SignalRChat.Core.Hubs;
global using SignalRChat.Core.Models;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage.ViewModel;
global using SignalRChat.Core.Modules.Login.ViewModel;
global using SignalRChat.Core.ViewModels;
global using System.IdentityModel.Tokens.Jwt;
global using System.Linq.Expressions;
global using System.Net;
global using System.Text.Json.Serialization;
